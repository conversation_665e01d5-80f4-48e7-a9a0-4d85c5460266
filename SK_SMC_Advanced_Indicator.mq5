//+------------------------------------------------------------------+
//|                                    SK_SMC_Advanced_Indicator.mq5 |
//|                                  Copyright 2024, ForexPro Team |
//|                                             https://forexpro.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, ForexPro Team"
#property link      "https://forexpro.com"
#property version   "1.00"
#property description "Advanced SK & SMC Combined Indicator"
#property description "SK: Key Levels & Fibonacci Analysis"
#property description "SMC: Smart Money Concepts Analysis"

#property indicator_chart_window
#property indicator_buffers 4
#property indicator_plots   2

//--- Plot settings
#property indicator_label1  "Buy Signal"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  3

#property indicator_label2  "Sell Signal"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  3

//--- Input parameters
input group "=== SK Strategy Settings ==="
input bool ShowFibonacci = true;                    // Show Fibonacci Levels
input bool ShowKeyLevels = true;                     // Show Key Support/Resistance
input int SwingPeriod = 20;                          // Swing High/Low Period
input double FibLevel1 = 38.2;                       // Fibonacci Level 1 (%)
input double FibLevel2 = 50.0;                       // Fibonacci Level 2 (%)
input double FibLevel3 = 61.8;                       // Fibonacci Level 3 (%)

input group "=== SMC Strategy Settings ==="
input bool ShowOrderBlocks = true;                   // Show Order Blocks
input bool ShowLiquidityPools = false;               // Show Liquidity Pools (Hidden by default)
input bool ShowBOS = true;                           // Show Break of Structure
input int OrderBlockPeriod = 15;                     // Order Block Detection Period
input int LiquidityLookback = 50;                    // Liquidity Pool Lookback

input group "=== Signal Settings ==="
input bool EnableBuySignals = true;                  // Enable Buy Signals
input bool EnableSellSignals = true;                 // Enable Sell Signals
input int SignalValidationBars = 3;                  // Signal Validation Bars
input double MinSignalStrength = 0.7;                // Minimum Signal Strength (0-1)
input bool ShowStopLoss = true;                      // Show Stop Loss Levels
input bool ShowTakeProfit = true;                    // Show Take Profit Levels
input double StopLossMultiplier = 1.5;               // Stop Loss Distance Multiplier
input double TakeProfitRatio = 2.0;                  // Take Profit Ratio (Risk:Reward)

input group "=== Visual Settings ==="
input color FibColor = clrGold;                      // Fibonacci Color
input color SupportColor = clrDodgerBlue;            // Support Level Color
input color ResistanceColor = clrOrangeRed;          // Resistance Level Color
input color OrderBlockBullish = clrLightGreen;       // Bullish Order Block Color
input color OrderBlockBearish = clrLightPink;        // Bearish Order Block Color
input color LiquidityColor = clrDarkSlateGray;       // Liquidity Pool Color (Subtle)
input color BOSColor = clrMagenta;                   // Break of Structure Color
input color StopLossColor = clrRed;                  // Stop Loss Color
input color TakeProfitColor = clrLime;               // Take Profit Color

input group "=== Alert Settings ==="
input bool EnableAlerts = true;                      // Enable Alerts
input bool EnableEmailAlerts = false;                // Enable Email Alerts
input bool EnablePushNotifications = false;          // Enable Push Notifications

//--- Indicator buffers
double BuySignalBuffer[];
double SellSignalBuffer[];
double TempBuffer1[];
double TempBuffer2[];

//--- Global variables
struct SwingPoint {
    datetime time;
    double price;
    bool isHigh;
};

struct OrderBlock {
    datetime startTime;
    datetime endTime;
    double highPrice;
    double lowPrice;
    bool isBullish;
    bool isValid;
};

struct LiquidityPool {
    double price;
    int strength;
    bool isHigh;
    datetime time;
};

SwingPoint swingPoints[];
OrderBlock orderBlocks[];
LiquidityPool liquidityPools[];

double lastSwingHigh = 0;
double lastSwingLow = 0;
datetime lastSwingHighTime = 0;
datetime lastSwingLowTime = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit() {
    //--- Set indicator buffers
    SetIndexBuffer(0, BuySignalBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SellSignalBuffer, INDICATOR_DATA);
    SetIndexBuffer(2, TempBuffer1, INDICATOR_CALCULATIONS);
    SetIndexBuffer(3, TempBuffer2, INDICATOR_CALCULATIONS);
    
    //--- Set arrow codes
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow for buy
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow for sell
    
    //--- Set empty values
    PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    
    //--- Initialize arrays
    ArrayResize(swingPoints, 0);
    ArrayResize(orderBlocks, 0);
    ArrayResize(liquidityPools, 0);
    
    //--- Set indicator name
    IndicatorSetString(INDICATOR_SHORTNAME, "SK_SMC_Advanced");
    
    //--- Set precision
    IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
    
    //--- Check for minimum bars
    if(rates_total < SwingPeriod + OrderBlockPeriod) return(0);
    
    //--- Calculate start position
    int start = MathMax(prev_calculated - 1, SwingPeriod);
    if(start < SwingPeriod) start = SwingPeriod;
    
    //--- Initialize buffers
    for(int i = start; i < rates_total; i++) {
        BuySignalBuffer[i] = EMPTY_VALUE;
        SellSignalBuffer[i] = EMPTY_VALUE;
    }
    
    //--- Main calculation loop
    for(int i = start; i < rates_total - 1; i++) {
        //--- Update swing points
        UpdateSwingPoints(i, high, low, time);
        
        //--- Update order blocks (SMC)
        if(ShowOrderBlocks) {
            UpdateOrderBlocks(i, open, high, low, close, time);
        }
        
        //--- Update liquidity pools (SMC)
        if(ShowLiquidityPools) {
            UpdateLiquidityPools(i, high, low, time);
        }
        
        //--- Generate signals
        double signalStrength = CalculateSignalStrength(i, open, high, low, close);
        
        if(signalStrength >= MinSignalStrength) {
            bool buyCondition = CheckBuyConditions(i, open, high, low, close);
            bool sellCondition = CheckSellConditions(i, open, high, low, close);
            
            if(EnableBuySignals && buyCondition) {
                BuySignalBuffer[i] = low[i] - (10 * _Point);

                // Calculate and draw Stop Loss and Take Profit for BUY signal
                if(ShowStopLoss || ShowTakeProfit) {
                    DrawTradeLevels(i, "BUY", close[i], high, low, time);
                }

                if(EnableAlerts && i == rates_total - 2) {
                    SendAlert("BUY Signal Generated", "SK_SMC Buy Signal at " + DoubleToString(close[i], _Digits));
                }
            }

            if(EnableSellSignals && sellCondition) {
                SellSignalBuffer[i] = high[i] + (10 * _Point);

                // Calculate and draw Stop Loss and Take Profit for SELL signal
                if(ShowStopLoss || ShowTakeProfit) {
                    DrawTradeLevels(i, "SELL", close[i], high, low, time);
                }

                if(EnableAlerts && i == rates_total - 2) {
                    SendAlert("SELL Signal Generated", "SK_SMC Sell Signal at " + DoubleToString(close[i], _Digits));
                }
            }
        }
    }
    
    //--- Draw visual elements
    DrawVisualElements();
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Update swing points for SK strategy                             |
//+------------------------------------------------------------------+
void UpdateSwingPoints(int index, const double &high[], const double &low[], const datetime &time[]) {
    if(index < SwingPeriod || index >= ArraySize(high) - SwingPeriod) return;
    
    //--- Check for swing high
    bool isSwingHigh = true;
    for(int j = index - SwingPeriod; j <= index + SwingPeriod; j++) {
        if(j != index && high[j] >= high[index]) {
            isSwingHigh = false;
            break;
        }
    }
    
    //--- Check for swing low
    bool isSwingLow = true;
    for(int j = index - SwingPeriod; j <= index + SwingPeriod; j++) {
        if(j != index && low[j] <= low[index]) {
            isSwingLow = false;
            break;
        }
    }
    
    //--- Add swing points
    if(isSwingHigh) {
        SwingPoint newPoint;
        newPoint.time = time[index];
        newPoint.price = high[index];
        newPoint.isHigh = true;
        
        int size = ArraySize(swingPoints);
        ArrayResize(swingPoints, size + 1);
        swingPoints[size] = newPoint;
        
        lastSwingHigh = high[index];
        lastSwingHighTime = time[index];
    }
    
    if(isSwingLow) {
        SwingPoint newPoint;
        newPoint.time = time[index];
        newPoint.price = low[index];
        newPoint.isHigh = false;
        
        int size = ArraySize(swingPoints);
        ArrayResize(swingPoints, size + 1);
        swingPoints[size] = newPoint;
        
        lastSwingLow = low[index];
        lastSwingLowTime = time[index];
    }
}

//+------------------------------------------------------------------+
//| Update order blocks for SMC strategy                            |
//+------------------------------------------------------------------+
void UpdateOrderBlocks(int index, const double &open[], const double &high[],
                      const double &low[], const double &close[], const datetime &time[]) {
    if(index < OrderBlockPeriod) return;

    //--- Look for bullish order block (strong buying pressure)
    bool isBullishOB = false;
    if(close[index] > open[index]) {  // Current bar is bullish
        double bodySize = close[index] - open[index];
        double avgBody = 0;

        // Calculate average body size
        for(int j = index - OrderBlockPeriod; j < index; j++) {
            avgBody += MathAbs(close[j] - open[j]);
        }
        avgBody /= OrderBlockPeriod;

        // Check if current body is significantly larger
        if(bodySize > avgBody * 1.5) {
            isBullishOB = true;
        }
    }

    //--- Look for bearish order block (strong selling pressure)
    bool isBearishOB = false;
    if(close[index] < open[index]) {  // Current bar is bearish
        double bodySize = open[index] - close[index];
        double avgBody = 0;

        // Calculate average body size
        for(int j = index - OrderBlockPeriod; j < index; j++) {
            avgBody += MathAbs(close[j] - open[j]);
        }
        avgBody /= OrderBlockPeriod;

        // Check if current body is significantly larger
        if(bodySize > avgBody * 1.5) {
            isBearishOB = true;
        }
    }

    //--- Add order block if found
    if(isBullishOB || isBearishOB) {
        OrderBlock newOB;
        newOB.startTime = time[index];
        newOB.endTime = time[index] + PeriodSeconds() * 20; // Extend 20 bars forward
        newOB.highPrice = high[index];
        newOB.lowPrice = low[index];
        newOB.isBullish = isBullishOB;
        newOB.isValid = true;

        int size = ArraySize(orderBlocks);
        ArrayResize(orderBlocks, size + 1);
        orderBlocks[size] = newOB;
    }
}

//+------------------------------------------------------------------+
//| Update liquidity pools for SMC strategy                         |
//+------------------------------------------------------------------+
void UpdateLiquidityPools(int index, const double &high[], const double &low[], const datetime &time[]) {
    if(index < LiquidityLookback) return;

    //--- Look for equal highs (liquidity pool)
    double currentHigh = high[index];
    int equalHighs = 0;

    for(int j = index - LiquidityLookback; j < index; j++) {
        if(MathAbs(high[j] - currentHigh) <= 2 * _Point) {
            equalHighs++;
        }
    }

    //--- Look for equal lows (liquidity pool)
    double currentLow = low[index];
    int equalLows = 0;

    for(int j = index - LiquidityLookback; j < index; j++) {
        if(MathAbs(low[j] - currentLow) <= 2 * _Point) {
            equalLows++;
        }
    }

    //--- Add liquidity pools if found
    if(equalHighs >= 3) {
        LiquidityPool newPool;
        newPool.price = currentHigh;
        newPool.strength = equalHighs;
        newPool.isHigh = true;
        newPool.time = time[index];

        int size = ArraySize(liquidityPools);
        ArrayResize(liquidityPools, size + 1);
        liquidityPools[size] = newPool;
    }

    if(equalLows >= 3) {
        LiquidityPool newPool;
        newPool.price = currentLow;
        newPool.strength = equalLows;
        newPool.isHigh = false;
        newPool.time = time[index];

        int size = ArraySize(liquidityPools);
        ArrayResize(liquidityPools, size + 1);
        liquidityPools[size] = newPool;
    }
}

//+------------------------------------------------------------------+
//| Calculate signal strength                                        |
//+------------------------------------------------------------------+
double CalculateSignalStrength(int index, const double &open[], const double &high[],
                              const double &low[], const double &close[]) {
    double strength = 0.0;

    //--- SK Strategy factors
    if(ShowFibonacci && lastSwingHigh > 0 && lastSwingLow > 0) {
        double fibRange = lastSwingHigh - lastSwingLow;
        double currentPrice = close[index];

        // Check proximity to Fibonacci levels
        double fib382 = lastSwingLow + (fibRange * FibLevel1 / 100.0);
        double fib500 = lastSwingLow + (fibRange * FibLevel2 / 100.0);
        double fib618 = lastSwingLow + (fibRange * FibLevel3 / 100.0);

        double minDistance = MathMin(MathMin(MathAbs(currentPrice - fib382),
                                           MathAbs(currentPrice - fib500)),
                                   MathAbs(currentPrice - fib618));

        if(minDistance <= 5 * _Point) {
            strength += 0.3;
        }
    }

    //--- SMC Strategy factors
    if(ShowOrderBlocks) {
        // Check if price is near order block
        for(int i = 0; i < ArraySize(orderBlocks); i++) {
            if(orderBlocks[i].isValid) {
                if(close[index] >= orderBlocks[i].lowPrice && close[index] <= orderBlocks[i].highPrice) {
                    strength += 0.25;
                    break;
                }
            }
        }
    }

    if(ShowLiquidityPools) {
        // Check if price is near liquidity pool
        for(int i = 0; i < ArraySize(liquidityPools); i++) {
            if(MathAbs(close[index] - liquidityPools[i].price) <= 3 * _Point) {
                strength += 0.2;
                break;
            }
        }
    }

    //--- Trend strength
    double trendStrength = CalculateTrendStrength(index, close);
    strength += trendStrength * 0.25;

    return MathMin(strength, 1.0);
}

//+------------------------------------------------------------------+
//| Calculate trend strength                                         |
//+------------------------------------------------------------------+
double CalculateTrendStrength(int index, const double &close[]) {
    if(index < 20) return 0.0;

    double ema20 = 0, ema50 = 0;

    // Simple EMA calculation
    for(int i = 0; i < 20; i++) {
        ema20 += close[index - i];
    }
    ema20 /= 20;

    for(int i = 0; i < 50 && (index - i) >= 0; i++) {
        ema50 += close[index - i];
    }
    ema50 /= MathMin(50, index + 1);

    if(close[index] > ema20 && ema20 > ema50) return 1.0;  // Strong uptrend
    if(close[index] < ema20 && ema20 < ema50) return 1.0;  // Strong downtrend
    if(close[index] > ema20) return 0.5;  // Weak uptrend
    if(close[index] < ema20) return 0.5;  // Weak downtrend

    return 0.0;  // No trend
}

//+------------------------------------------------------------------+
//| Check buy conditions                                             |
//+------------------------------------------------------------------+
bool CheckBuyConditions(int index, const double &open[], const double &high[],
                       const double &low[], const double &close[]) {
    bool skCondition = false;
    bool smcCondition = false;

    //--- SK Strategy buy condition
    if(ShowFibonacci && lastSwingHigh > 0 && lastSwingLow > 0) {
        double fibRange = lastSwingHigh - lastSwingLow;
        double fib618 = lastSwingLow + (fibRange * FibLevel3 / 100.0);
        double fib500 = lastSwingLow + (fibRange * FibLevel2 / 100.0);

        // Price bouncing from Fibonacci support
        if(low[index] <= fib618 + 2 * _Point && close[index] > fib618 &&
           close[index] > open[index]) {
            skCondition = true;
        }

        if(low[index] <= fib500 + 2 * _Point && close[index] > fib500 &&
           close[index] > open[index]) {
            skCondition = true;
        }
    }

    //--- SMC Strategy buy condition
    if(ShowOrderBlocks) {
        for(int i = 0; i < ArraySize(orderBlocks); i++) {
            if(orderBlocks[i].isValid && orderBlocks[i].isBullish) {
                // Price reacting from bullish order block
                if(low[index] <= orderBlocks[i].highPrice &&
                   low[index] >= orderBlocks[i].lowPrice &&
                   close[index] > open[index]) {
                    smcCondition = true;
                    break;
                }
            }
        }
    }

    //--- Liquidity sweep condition
    if(ShowLiquidityPools) {
        for(int i = 0; i < ArraySize(liquidityPools); i++) {
            if(!liquidityPools[i].isHigh) {  // Low liquidity pool
                if(low[index] <= liquidityPools[i].price &&
                   close[index] > liquidityPools[i].price + 3 * _Point) {
                    smcCondition = true;
                    break;
                }
            }
        }
    }

    //--- Trend confirmation
    bool trendConfirmation = CalculateTrendStrength(index, close) > 0.3;

    return (skCondition || smcCondition) && trendConfirmation;
}

//+------------------------------------------------------------------+
//| Check sell conditions                                            |
//+------------------------------------------------------------------+
bool CheckSellConditions(int index, const double &open[], const double &high[],
                        const double &low[], const double &close[]) {
    bool skCondition = false;
    bool smcCondition = false;

    //--- SK Strategy sell condition
    if(ShowFibonacci && lastSwingHigh > 0 && lastSwingLow > 0) {
        double fibRange = lastSwingHigh - lastSwingLow;
        double fib618 = lastSwingLow + (fibRange * FibLevel3 / 100.0);
        double fib500 = lastSwingLow + (fibRange * FibLevel2 / 100.0);

        // Price rejecting from Fibonacci resistance
        if(high[index] >= fib618 - 2 * _Point && close[index] < fib618 &&
           close[index] < open[index]) {
            skCondition = true;
        }

        if(high[index] >= fib500 - 2 * _Point && close[index] < fib500 &&
           close[index] < open[index]) {
            skCondition = true;
        }
    }

    //--- SMC Strategy sell condition
    if(ShowOrderBlocks) {
        for(int i = 0; i < ArraySize(orderBlocks); i++) {
            if(orderBlocks[i].isValid && !orderBlocks[i].isBullish) {
                // Price reacting from bearish order block
                if(high[index] >= orderBlocks[i].lowPrice &&
                   high[index] <= orderBlocks[i].highPrice &&
                   close[index] < open[index]) {
                    smcCondition = true;
                    break;
                }
            }
        }
    }

    //--- Liquidity sweep condition
    if(ShowLiquidityPools) {
        for(int i = 0; i < ArraySize(liquidityPools); i++) {
            if(liquidityPools[i].isHigh) {  // High liquidity pool
                if(high[index] >= liquidityPools[i].price &&
                   close[index] < liquidityPools[i].price - 3 * _Point) {
                    smcCondition = true;
                    break;
                }
            }
        }
    }

    //--- Trend confirmation
    bool trendConfirmation = CalculateTrendStrength(index, close) < -0.3;

    return (skCondition || smcCondition) && trendConfirmation;
}

//+------------------------------------------------------------------+
//| Draw visual elements on chart                                   |
//+------------------------------------------------------------------+
void DrawVisualElements() {
    //--- Draw Fibonacci levels
    if(ShowFibonacci && lastSwingHigh > 0 && lastSwingLow > 0) {
        DrawFibonacciLevels();
    }

    //--- Draw key levels
    if(ShowKeyLevels) {
        DrawKeyLevels();
    }

    //--- Draw order blocks
    if(ShowOrderBlocks) {
        DrawOrderBlocks();
    }

    //--- Draw liquidity pools
    if(ShowLiquidityPools) {
        DrawLiquidityPools();
    }

    //--- Draw break of structure
    if(ShowBOS) {
        DrawBreakOfStructure();
    }
}

//+------------------------------------------------------------------+
//| Draw Fibonacci levels                                           |
//+------------------------------------------------------------------+
void DrawFibonacciLevels() {
    if(lastSwingHigh <= lastSwingLow) return;

    double fibRange = lastSwingHigh - lastSwingLow;

    //--- Draw Fibonacci levels
    double fib382 = lastSwingLow + (fibRange * FibLevel1 / 100.0);
    double fib500 = lastSwingLow + (fibRange * FibLevel2 / 100.0);
    double fib618 = lastSwingLow + (fibRange * FibLevel3 / 100.0);

    string prefix = "Fib_";

    //--- 38.2% level
    ObjectCreate(0, prefix + "382", OBJ_HLINE, 0, 0, fib382);
    ObjectSetInteger(0, prefix + "382", OBJPROP_COLOR, FibColor);
    ObjectSetInteger(0, prefix + "382", OBJPROP_STYLE, STYLE_DOT);
    ObjectSetString(0, prefix + "382", OBJPROP_TEXT, "Fib 38.2%");

    //--- 50% level
    ObjectCreate(0, prefix + "500", OBJ_HLINE, 0, 0, fib500);
    ObjectSetInteger(0, prefix + "500", OBJPROP_COLOR, FibColor);
    ObjectSetInteger(0, prefix + "500", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetString(0, prefix + "500", OBJPROP_TEXT, "Fib 50%");

    //--- 61.8% level
    ObjectCreate(0, prefix + "618", OBJ_HLINE, 0, 0, fib618);
    ObjectSetInteger(0, prefix + "618", OBJPROP_COLOR, FibColor);
    ObjectSetInteger(0, prefix + "618", OBJPROP_STYLE, STYLE_DOT);
    ObjectSetString(0, prefix + "618", OBJPROP_TEXT, "Fib 61.8%");
}

//+------------------------------------------------------------------+
//| Draw key support and resistance levels                          |
//+------------------------------------------------------------------+
void DrawKeyLevels() {
    string prefix = "KeyLevel_";

    //--- Draw swing high as resistance
    if(lastSwingHigh > 0) {
        ObjectCreate(0, prefix + "Resistance", OBJ_HLINE, 0, 0, lastSwingHigh);
        ObjectSetInteger(0, prefix + "Resistance", OBJPROP_COLOR, ResistanceColor);
        ObjectSetInteger(0, prefix + "Resistance", OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, prefix + "Resistance", OBJPROP_WIDTH, 2);
        ObjectSetString(0, prefix + "Resistance", OBJPROP_TEXT, "Resistance");
    }

    //--- Draw swing low as support
    if(lastSwingLow > 0) {
        ObjectCreate(0, prefix + "Support", OBJ_HLINE, 0, 0, lastSwingLow);
        ObjectSetInteger(0, prefix + "Support", OBJPROP_COLOR, SupportColor);
        ObjectSetInteger(0, prefix + "Support", OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, prefix + "Support", OBJPROP_WIDTH, 2);
        ObjectSetString(0, prefix + "Support", OBJPROP_TEXT, "Support");
    }
}

//+------------------------------------------------------------------+
//| Draw order blocks                                               |
//+------------------------------------------------------------------+
void DrawOrderBlocks() {
    string prefix = "OrderBlock_";

    for(int i = 0; i < ArraySize(orderBlocks); i++) {
        if(!orderBlocks[i].isValid) continue;

        string objName = prefix + IntegerToString(i);

        ObjectCreate(0, objName, OBJ_RECTANGLE, 0,
                    orderBlocks[i].startTime, orderBlocks[i].lowPrice,
                    orderBlocks[i].endTime, orderBlocks[i].highPrice);

        color blockColor = orderBlocks[i].isBullish ? OrderBlockBullish : OrderBlockBearish;
        ObjectSetInteger(0, objName, OBJPROP_COLOR, blockColor);
        ObjectSetInteger(0, objName, OBJPROP_FILL, true);
        ObjectSetInteger(0, objName, OBJPROP_BACK, true);
        ObjectSetString(0, objName, OBJPROP_TEXT,
                       orderBlocks[i].isBullish ? "Bullish OB" : "Bearish OB");
    }
}

//+------------------------------------------------------------------+
//| Draw liquidity pools (subtle zones instead of lines)           |
//+------------------------------------------------------------------+
void DrawLiquidityPools() {
    if(!ShowLiquidityPools) return;

    string prefix = "LiquidityZone_";

    for(int i = 0; i < ArraySize(liquidityPools); i++) {
        string objName = prefix + IntegerToString(i);

        // Create subtle zones instead of obvious lines
        double zoneHeight = 5 * _Point; // Small zone height
        double upperLevel = liquidityPools[i].price + zoneHeight;
        double lowerLevel = liquidityPools[i].price - zoneHeight;

        // Create rectangle zone
        datetime startTime = liquidityPools[i].time;
        datetime endTime = startTime + PeriodSeconds() * 10; // Extend 10 bars

        ObjectCreate(0, objName, OBJ_RECTANGLE, 0, startTime, lowerLevel, endTime, upperLevel);
        ObjectSetInteger(0, objName, OBJPROP_COLOR, LiquidityColor);
        ObjectSetInteger(0, objName, OBJPROP_FILL, true);
        ObjectSetInteger(0, objName, OBJPROP_BACK, true);
        ObjectSetInteger(0, objName, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, objName, OBJPROP_WIDTH, 1);
        ObjectSetString(0, objName, OBJPROP_TEXT,
                       "LQ-" + IntegerToString(liquidityPools[i].strength));
    }
}

//+------------------------------------------------------------------+
//| Draw break of structure                                         |
//+------------------------------------------------------------------+
void DrawBreakOfStructure() {
    // Implementation for BOS detection and drawing
    // This would require more complex logic to detect structure breaks
    // For now, we'll mark significant swing breaks

    if(ArraySize(swingPoints) < 2) return;

    string prefix = "BOS_";
    int bosCount = 0;

    for(int i = 1; i < ArraySize(swingPoints); i++) {
        SwingPoint current = swingPoints[i];
        SwingPoint previous = swingPoints[i-1];

        bool isBOS = false;

        if(current.isHigh && previous.isHigh && current.price > previous.price) {
            isBOS = true;  // Higher high
        }
        else if(!current.isHigh && !previous.isHigh && current.price < previous.price) {
            isBOS = true;  // Lower low
        }

        if(isBOS) {
            string objName = prefix + IntegerToString(bosCount++);
            ObjectCreate(0, objName, OBJ_ARROW, 0, current.time, current.price);
            ObjectSetInteger(0, objName, OBJPROP_ARROWCODE, 159);
            ObjectSetInteger(0, objName, OBJPROP_COLOR, BOSColor);
            ObjectSetInteger(0, objName, OBJPROP_WIDTH, 3);
        }
    }
}

//+------------------------------------------------------------------+
//| Draw trade levels (Stop Loss and Take Profit)                  |
//+------------------------------------------------------------------+
void DrawTradeLevels(int signalIndex, string signalType, double entryPrice,
                    const double &high[], const double &low[], const datetime &time[]) {

    double stopLoss = 0;
    double takeProfit = 0;

    // Calculate Stop Loss and Take Profit levels
    if(signalType == "BUY") {
        // For BUY signals
        // Stop Loss: Below recent swing low
        double recentLow = FindRecentSwingLow(signalIndex, low, 20);
        stopLoss = recentLow - (StopLossMultiplier * 10 * _Point);

        // Take Profit: Risk:Reward ratio
        double riskDistance = entryPrice - stopLoss;
        takeProfit = entryPrice + (riskDistance * TakeProfitRatio);
    }
    else if(signalType == "SELL") {
        // For SELL signals
        // Stop Loss: Above recent swing high
        double recentHigh = FindRecentSwingHigh(signalIndex, high, 20);
        stopLoss = recentHigh + (StopLossMultiplier * 10 * _Point);

        // Take Profit: Risk:Reward ratio
        double riskDistance = stopLoss - entryPrice;
        takeProfit = entryPrice - (riskDistance * TakeProfitRatio);
    }

    // Create unique names for objects
    string prefix = signalType + "_" + IntegerToString(signalIndex) + "_";
    datetime signalTime = time[signalIndex];
    datetime endTime = signalTime + PeriodSeconds() * 50; // Extend 50 bars

    // Draw Stop Loss line
    if(ShowStopLoss && stopLoss > 0) {
        string slName = prefix + "SL";
        ObjectCreate(0, slName, OBJ_TREND, 0, signalTime, stopLoss, endTime, stopLoss);
        ObjectSetInteger(0, slName, OBJPROP_COLOR, StopLossColor);
        ObjectSetInteger(0, slName, OBJPROP_STYLE, STYLE_DASH);
        ObjectSetInteger(0, slName, OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, slName, OBJPROP_RAY_RIGHT, false);
        ObjectSetString(0, slName, OBJPROP_TEXT, "SL: " + DoubleToString(stopLoss, _Digits));

        // Add text label
        string slTextName = prefix + "SL_Text";
        ObjectCreate(0, slTextName, OBJ_TEXT, 0, endTime, stopLoss);
        ObjectSetString(0, slTextName, OBJPROP_TEXT, "SL");
        ObjectSetInteger(0, slTextName, OBJPROP_COLOR, StopLossColor);
        ObjectSetInteger(0, slTextName, OBJPROP_FONTSIZE, 8);
    }

    // Draw Take Profit line
    if(ShowTakeProfit && takeProfit > 0) {
        string tpName = prefix + "TP";
        ObjectCreate(0, tpName, OBJ_TREND, 0, signalTime, takeProfit, endTime, takeProfit);
        ObjectSetInteger(0, tpName, OBJPROP_COLOR, TakeProfitColor);
        ObjectSetInteger(0, tpName, OBJPROP_STYLE, STYLE_DASH);
        ObjectSetInteger(0, tpName, OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, tpName, OBJPROP_RAY_RIGHT, false);
        ObjectSetString(0, tpName, OBJPROP_TEXT, "TP: " + DoubleToString(takeProfit, _Digits));

        // Add text label
        string tpTextName = prefix + "TP_Text";
        ObjectCreate(0, tpTextName, OBJ_TEXT, 0, endTime, takeProfit);
        ObjectSetString(0, tpTextName, OBJPROP_TEXT, "TP");
        ObjectSetInteger(0, tpTextName, OBJPROP_COLOR, TakeProfitColor);
        ObjectSetInteger(0, tpTextName, OBJPROP_FONTSIZE, 8);
    }

    // Draw entry level line
    string entryName = prefix + "Entry";
    ObjectCreate(0, entryName, OBJ_TREND, 0, signalTime, entryPrice, endTime, entryPrice);
    ObjectSetInteger(0, entryName, OBJPROP_COLOR, signalType == "BUY" ? clrLime : clrRed);
    ObjectSetInteger(0, entryName, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, entryName, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, entryName, OBJPROP_RAY_RIGHT, false);
    ObjectSetString(0, entryName, OBJPROP_TEXT, "Entry: " + DoubleToString(entryPrice, _Digits));
}

//+------------------------------------------------------------------+
//| Find recent swing high                                          |
//+------------------------------------------------------------------+
double FindRecentSwingHigh(int currentIndex, const double &high[], int lookback) {
    double maxHigh = 0;
    int startIndex = MathMax(0, currentIndex - lookback);

    for(int i = startIndex; i <= currentIndex; i++) {
        if(high[i] > maxHigh) {
            maxHigh = high[i];
        }
    }

    return maxHigh;
}

//+------------------------------------------------------------------+
//| Find recent swing low                                           |
//+------------------------------------------------------------------+
double FindRecentSwingLow(int currentIndex, const double &low[], int lookback) {
    double minLow = 999999;
    int startIndex = MathMax(0, currentIndex - lookback);

    for(int i = startIndex; i <= currentIndex; i++) {
        if(low[i] < minLow) {
            minLow = low[i];
        }
    }

    return minLow;
}

//+------------------------------------------------------------------+
//| Send alert notification                                         |
//+------------------------------------------------------------------+
void SendAlert(string title, string message) {
    if(EnableAlerts) {
        Alert(title + ": " + message);
    }

    if(EnableEmailAlerts) {
        SendMail(title, message);
    }

    if(EnablePushNotifications) {
        SendNotification(message);
    }
}

//+------------------------------------------------------------------+
//| Cleanup function                                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    //--- Remove all objects created by indicator
    ObjectsDeleteAll(0, "Fib_");
    ObjectsDeleteAll(0, "KeyLevel_");
    ObjectsDeleteAll(0, "OrderBlock_");
    ObjectsDeleteAll(0, "LiquidityZone_");
    ObjectsDeleteAll(0, "BOS_");
    ObjectsDeleteAll(0, "BUY_");
    ObjectsDeleteAll(0, "SELL_");

    Comment("");
}
