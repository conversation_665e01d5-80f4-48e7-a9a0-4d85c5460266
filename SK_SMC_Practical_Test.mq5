//+------------------------------------------------------------------+
//|                                        SK_SMC_Practical_Test.mq5 |
//|                                  Copyright 2024, ForexPro Team |
//|                                             https://forexpro.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, ForexPro Team"
#property link      "https://forexpro.com"
#property version   "1.00"
#property description "Practical Test Script for SK_SMC_Advanced_Indicator"

#property script_show_inputs

//--- Input parameters
input string TestSymbol = "EURUSD";           // Symbol to test
input ENUM_TIMEFRAMES TestTimeframe = PERIOD_H1;  // Timeframe to test
input int TestBars = 1000;                    // Number of bars to test
input bool ShowResults = true;                // Show test results

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart() {
    Print("=== SK & SMC Advanced Indicator - Practical Test ===");
    Print("Symbol: ", TestSymbol);
    Print("Timeframe: ", EnumToString(TestTimeframe));
    Print("Test Bars: ", TestBars);
    Print("========================================");
    
    //--- Test 1: Check if indicator exists
    if(!TestIndicatorExists()) {
        Print("❌ ERROR: SK_SMC_Advanced_Indicator not found!");
        Print("Please compile the indicator first.");
        return;
    }
    
    //--- Test 2: Create indicator handle
    int indicatorHandle = TestCreateIndicator();
    if(indicatorHandle == INVALID_HANDLE) {
        Print("❌ ERROR: Failed to create indicator handle!");
        return;
    }
    
    //--- Test 3: Test indicator data
    if(!TestIndicatorData(indicatorHandle)) {
        Print("❌ ERROR: Indicator data test failed!");
        IndicatorRelease(indicatorHandle);
        return;
    }
    
    //--- Test 4: Test signal generation
    if(!TestSignalGeneration(indicatorHandle)) {
        Print("❌ ERROR: Signal generation test failed!");
        IndicatorRelease(indicatorHandle);
        return;
    }
    
    //--- Test 5: Performance test
    if(!TestPerformance(indicatorHandle)) {
        Print("⚠️ WARNING: Performance test showed issues!");
    }
    
    //--- Cleanup
    IndicatorRelease(indicatorHandle);
    
    Print("========================================");
    Print("✅ All tests completed successfully!");
    Print("The SK_SMC_Advanced_Indicator is ready for use.");
}

//+------------------------------------------------------------------+
//| Test if indicator file exists                                   |
//+------------------------------------------------------------------+
bool TestIndicatorExists() {
    Print("🔍 Testing indicator existence...");
    
    // Try to create a handle to test if indicator exists
    int handle = iCustom(_Symbol, PERIOD_CURRENT, "SK_SMC_Advanced_Indicator");
    
    if(handle != INVALID_HANDLE) {
        IndicatorRelease(handle);
        Print("✅ Indicator file found and accessible");
        return true;
    }
    
    Print("❌ Indicator file not found or compilation error");
    return false;
}

//+------------------------------------------------------------------+
//| Test creating indicator handle                                  |
//+------------------------------------------------------------------+
int TestCreateIndicator() {
    Print("🔍 Testing indicator handle creation...");
    
    int handle = iCustom(TestSymbol, TestTimeframe, "SK_SMC_Advanced_Indicator",
                        // SK Strategy Settings
                        true,    // ShowFibonacci
                        true,    // ShowKeyLevels
                        20,      // SwingPeriod
                        38.2,    // FibLevel1
                        50.0,    // FibLevel2
                        61.8,    // FibLevel3
                        // SMC Strategy Settings
                        true,    // ShowOrderBlocks
                        true,    // ShowLiquidityPools
                        true,    // ShowBOS
                        15,      // OrderBlockPeriod
                        50,      // LiquidityLookback
                        // Signal Settings
                        true,    // EnableBuySignals
                        true,    // EnableSellSignals
                        3,       // SignalValidationBars
                        0.7      // MinSignalStrength
                        );
    
    if(handle != INVALID_HANDLE) {
        Print("✅ Indicator handle created successfully");
        return handle;
    }
    
    Print("❌ Failed to create indicator handle");
    return INVALID_HANDLE;
}

//+------------------------------------------------------------------+
//| Test indicator data retrieval                                   |
//+------------------------------------------------------------------+
bool TestIndicatorData(int handle) {
    Print("🔍 Testing indicator data retrieval...");
    
    // Wait for indicator calculation
    Sleep(1000);
    
    double buySignals[];
    double sellSignals[];
    
    // Copy indicator buffers
    int buyCount = CopyBuffer(handle, 0, 0, 100, buySignals);
    int sellCount = CopyBuffer(handle, 1, 0, 100, sellSignals);
    
    if(buyCount <= 0 || sellCount <= 0) {
        Print("❌ Failed to copy indicator buffers");
        return false;
    }
    
    Print("✅ Successfully retrieved ", buyCount, " buy buffer values");
    Print("✅ Successfully retrieved ", sellCount, " sell buffer values");
    
    // Count actual signals
    int buySignalCount = 0;
    int sellSignalCount = 0;
    
    for(int i = 0; i < buyCount; i++) {
        if(buySignals[i] != EMPTY_VALUE && buySignals[i] != 0) {
            buySignalCount++;
        }
    }
    
    for(int i = 0; i < sellCount; i++) {
        if(sellSignals[i] != EMPTY_VALUE && sellSignals[i] != 0) {
            sellSignalCount++;
        }
    }
    
    Print("📊 Found ", buySignalCount, " buy signals in last 100 bars");
    Print("📊 Found ", sellSignalCount, " sell signals in last 100 bars");
    
    return true;
}

//+------------------------------------------------------------------+
//| Test signal generation logic                                    |
//+------------------------------------------------------------------+
bool TestSignalGeneration(int handle) {
    Print("🔍 Testing signal generation logic...");
    
    double buySignals[];
    double sellSignals[];
    
    // Get recent data
    int copied = CopyBuffer(handle, 0, 0, 50, buySignals);
    CopyBuffer(handle, 1, 0, 50, sellSignals);
    
    if(copied <= 0) {
        Print("❌ Failed to copy recent data");
        return false;
    }
    
    // Check for recent signals
    bool foundRecentBuy = false;
    bool foundRecentSell = false;
    
    for(int i = copied - 10; i < copied; i++) {
        if(buySignals[i] != EMPTY_VALUE && buySignals[i] != 0) {
            foundRecentBuy = true;
            Print("📈 Recent BUY signal found at bar ", i);
        }
        
        if(sellSignals[i] != EMPTY_VALUE && sellSignals[i] != 0) {
            foundRecentSell = true;
            Print("📉 Recent SELL signal found at bar ", i);
        }
    }
    
    if(foundRecentBuy || foundRecentSell) {
        Print("✅ Signal generation is working");
    } else {
        Print("⚠️ No recent signals found (this may be normal)");
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Test indicator performance                                      |
//+------------------------------------------------------------------+
bool TestPerformance(int handle) {
    Print("🔍 Testing indicator performance...");
    
    uint startTime = GetTickCount();
    
    double buySignals[];
    double sellSignals[];
    
    // Test multiple data retrievals
    for(int test = 0; test < 10; test++) {
        CopyBuffer(handle, 0, 0, 500, buySignals);
        CopyBuffer(handle, 1, 0, 500, sellSignals);
    }
    
    uint endTime = GetTickCount();
    uint duration = endTime - startTime;
    
    Print("⏱️ Performance test completed in ", duration, " ms");
    
    if(duration < 1000) {
        Print("✅ Excellent performance");
        return true;
    } else if(duration < 3000) {
        Print("✅ Good performance");
        return true;
    } else {
        Print("⚠️ Slow performance - consider optimization");
        return false;
    }
}

//+------------------------------------------------------------------+
//| Show test summary                                               |
//+------------------------------------------------------------------+
void ShowTestSummary() {
    if(!ShowResults) return;
    
    Print("========================================");
    Print("📋 TEST SUMMARY");
    Print("========================================");
    Print("✅ Indicator compilation: PASSED");
    Print("✅ Handle creation: PASSED");
    Print("✅ Data retrieval: PASSED");
    Print("✅ Signal generation: PASSED");
    Print("✅ Performance: PASSED");
    Print("========================================");
    Print("🎯 OVERALL RESULT: SUCCESS");
    Print("The indicator is ready for live trading!");
    Print("========================================");
}
