const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const dotenv = require('dotenv');
const { createServer } = require('http');
const { Server } = require('socket.io');

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);

// Socket.io setup
const io = new Server(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || "http://localhost:3000",
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Mock market data
const marketData = {
  'EUR/USD': { basePrice: 1.0856, name: 'Euro / US Dollar' },
  'GBP/USD': { basePrice: 1.2734, name: 'British Pound / US Dollar' },
  'USD/JPY': { basePrice: 149.85, name: 'US Dollar / Japanese Yen' },
  'XAU/USD': { basePrice: 2034.50, name: 'Gold / US Dollar' },
  'USD/CHF': { basePrice: 0.8756, name: 'US Dollar / Swiss Franc' },
  'AUD/USD': { basePrice: 0.6823, name: 'Australian Dollar / US Dollar' },
  'USD/CAD': { basePrice: 1.3456, name: 'US Dollar / Canadian Dollar' },
  'NZD/USD': { basePrice: 0.6234, name: 'New Zealand Dollar / US Dollar' }
};

const generateMockPrice = (basePrice, volatility = 0.01) => {
  const change = (Math.random() - 0.5) * volatility;
  return basePrice * (1 + change);
};

// API Routes
app.get('/api/market/prices', (req, res) => {
  try {
    const prices = Object.entries(marketData).map(([symbol, data]) => {
      const currentPrice = generateMockPrice(data.basePrice);
      const previousPrice = generateMockPrice(data.basePrice);
      const change = currentPrice - previousPrice;
      const changePercent = (change / previousPrice) * 100;

      return {
        symbol,
        name: data.name,
        price: Number(currentPrice.toFixed(4)),
        change: Number(change.toFixed(4)),
        changePercent: Number(changePercent.toFixed(2)),
        volume: `${(Math.random() * 2 + 0.5).toFixed(1)}B`,
        high24h: Number((currentPrice * 1.02).toFixed(4)),
        low24h: Number((currentPrice * 0.98).toFixed(4)),
        timestamp: new Date().toISOString()
      };
    });

    res.json({ prices });
  } catch (error) {
    console.error('Error fetching prices:', error);
    res.status(500).json({ message: 'Error fetching market prices' });
  }
});

// Get historical data for charts
app.get('/api/market/history/:symbol', (req, res) => {
  try {
    const { symbol } = req.params;
    const { timeframe = '1h', limit = 24 } = req.query;
    
    const symbolData = marketData[symbol];
    if (!symbolData) {
      return res.status(404).json({ message: 'Symbol not found' });
    }

    const data = [];
    const now = new Date();
    const intervalMs = timeframe === '1h' ? 60 * 60 * 1000 : 60 * 1000;

    for (let i = Number(limit) - 1; i >= 0; i--) {
      const time = new Date(now.getTime() - i * intervalMs);
      const price = generateMockPrice(symbolData.basePrice);
      
      data.push({
        time: time.toISOString(),
        price: Number(price.toFixed(4)),
        volume: Math.floor(Math.random() * 1000000) + 500000,
        open: Number((price * 0.999).toFixed(4)),
        high: Number((price * 1.001).toFixed(4)),
        low: Number((price * 0.998).toFixed(4)),
        close: Number(price.toFixed(4))
      });
    }

    res.json({ symbol, timeframe, data });
  } catch (error) {
    console.error('Error fetching historical data:', error);
    res.status(500).json({ message: 'Error fetching historical data' });
  }
});

// Mock signals
const signals = [
  {
    id: 1,
    symbol: 'EUR/USD',
    type: 'BUY',
    confidence: 85,
    entry: 1.0850,
    target: 1.0890,
    stopLoss: 1.0820,
    status: 'active',
    reason: 'Strong bullish momentum with RSI oversold recovery',
    createdAt: new Date(Date.now() - 2 * 60 * 1000),
    timeAgo: '2 minutes ago'
  },
  {
    id: 2,
    symbol: 'GBP/USD',
    type: 'SELL',
    confidence: 78,
    entry: 1.2740,
    target: 1.2700,
    stopLoss: 1.2780,
    status: 'active',
    reason: 'Bearish divergence on 4H chart with resistance rejection',
    createdAt: new Date(Date.now() - 15 * 60 * 1000),
    timeAgo: '15 minutes ago'
  },
  {
    id: 3,
    symbol: 'XAU/USD',
    type: 'BUY',
    confidence: 92,
    entry: 2030.00,
    target: 2050.00,
    stopLoss: 2015.00,
    status: 'completed',
    reason: 'Gold breaking above key resistance with high volume',
    createdAt: new Date(Date.now() - 60 * 60 * 1000),
    timeAgo: '1 hour ago'
  }
];

app.get('/api/signals', (req, res) => {
  res.json({ signals });
});

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('join-trading', (userId) => {
    socket.join(`user-${userId}`);
    console.log(`User ${userId} joined trading room`);
  });

  socket.on('subscribe-prices', (symbols) => {
    if (Array.isArray(symbols)) {
      symbols.forEach((symbol) => {
        socket.join(`price-${symbol}`);
      });
    }
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

// Start real-time price updates
const startPriceUpdates = () => {
  setInterval(() => {
    const updates = Object.entries(marketData).map(([symbol, data]) => {
      const currentPrice = generateMockPrice(data.basePrice);
      const previousPrice = generateMockPrice(data.basePrice);
      const change = currentPrice - previousPrice;
      const changePercent = (change / previousPrice) * 100;

      return {
        symbol,
        price: Number(currentPrice.toFixed(4)),
        change: Number(change.toFixed(4)),
        changePercent: Number(changePercent.toFixed(2)),
        timestamp: new Date().toISOString()
      };
    });

    io.emit('price-updates', updates);

    updates.forEach(update => {
      io.to(`price-${update.symbol}`).emit('price-update', update);
    });
  }, 5000);
};

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ message: 'Route not found' });
});

const PORT = process.env.PORT || 5001;

server.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 CORS enabled for: ${process.env.CORS_ORIGIN || 'http://localhost:3000'}`);
  
  // Start price updates
  startPriceUpdates();
  console.log('✅ Socket services initialized');
  console.log('📈 Real-time price updates started');
});

module.exports = app;
