{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/robot/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { clsx } from 'clsx';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  title?: string;\n  subtitle?: string;\n  variant?: 'default' | 'glass' | 'chart';\n}\n\nexport const Card: React.FC<CardProps> = ({\n  children,\n  className,\n  title,\n  subtitle,\n  variant = 'default'\n}) => {\n  const baseClasses = 'rounded-lg p-6 shadow-lg';\n  \n  const variantClasses = {\n    default: 'bg-slate-800 border border-slate-700',\n    glass: 'glass-effect',\n    chart: 'chart-container'\n  };\n\n  return (\n    <div className={clsx(baseClasses, variantClasses[variant], className)}>\n      {(title || subtitle) && (\n        <div className=\"mb-4\">\n          {title && (\n            <h3 className=\"text-lg font-semibold text-white\">{title}</h3>\n          )}\n          {subtitle && (\n            <p className=\"text-sm text-gray-400 mt-1\">{subtitle}</p>\n          )}\n        </div>\n      )}\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;;;AAUO,MAAM,OAA4B,CAAC,EACxC,QAAQ,EACR,SAAS,EACT,KAAK,EACL,QAAQ,EACR,UAAU,SAAS,EACpB;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,OAAO;QACP,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,aAAa,cAAc,CAAC,QAAQ,EAAE;;YACxD,CAAC,SAAS,QAAQ,mBACjB,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;oBAEnD,0BACC,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;YAIhD;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/robot/frontend/src/components/trading/PriceCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card } from '../ui/Card';\nimport { ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';\n\ninterface PriceCardProps {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume?: string;\n  high24h?: number;\n  low24h?: number;\n}\n\nexport const PriceCard: React.FC<PriceCardProps> = ({\n  symbol,\n  name,\n  price,\n  change,\n  changePercent,\n  volume,\n  high24h,\n  low24h\n}) => {\n  const isPositive = change >= 0;\n  const priceClass = isPositive ? 'price-up' : 'price-down';\n  const TrendIcon = isPositive ? ArrowTrendingUpIcon : ArrowTrendingDownIcon;\n\n  return (\n    <Card variant=\"glass\" className=\"hover:scale-105 transition-transform duration-200\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div>\n          <h3 className=\"text-lg font-bold text-white\">{symbol}</h3>\n          <p className=\"text-sm text-gray-400\">{name}</p>\n        </div>\n        <TrendIcon className={`w-6 h-6 ${priceClass}`} />\n      </div>\n      \n      <div className=\"space-y-2\">\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-2xl font-bold text-white\">\n            ${price.toFixed(4)}\n          </span>\n          <div className={`flex items-center space-x-1 ${priceClass}`}>\n            <span className=\"text-sm font-medium\">\n              {isPositive ? '+' : ''}{change.toFixed(4)}\n            </span>\n            <span className=\"text-xs\">\n              ({isPositive ? '+' : ''}{changePercent.toFixed(2)}%)\n            </span>\n          </div>\n        </div>\n        \n        {(high24h || low24h || volume) && (\n          <div className=\"pt-2 border-t border-slate-600 space-y-1\">\n            {high24h && (\n              <div className=\"flex justify-between text-xs\">\n                <span className=\"text-gray-400\">24h High:</span>\n                <span className=\"text-white\">${high24h.toFixed(4)}</span>\n              </div>\n            )}\n            {low24h && (\n              <div className=\"flex justify-between text-xs\">\n                <span className=\"text-gray-400\">24h Low:</span>\n                <span className=\"text-white\">${low24h.toFixed(4)}</span>\n              </div>\n            )}\n            {volume && (\n              <div className=\"flex justify-between text-xs\">\n                <span className=\"text-gray-400\">Volume:</span>\n                <span className=\"text-white\">{volume}</span>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </Card>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;;;;AAaO,MAAM,YAAsC,CAAC,EAClD,MAAM,EACN,IAAI,EACJ,KAAK,EACL,MAAM,EACN,aAAa,EACb,MAAM,EACN,OAAO,EACP,MAAM,EACP;IACC,MAAM,aAAa,UAAU;IAC7B,MAAM,aAAa,aAAa,aAAa;IAC7C,MAAM,YAAY,aAAa,qOAAA,CAAA,sBAAmB,GAAG,yOAAA,CAAA,wBAAqB;IAE1E,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,SAAQ;QAAQ,WAAU;;0BAC9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAExC,8OAAC;wBAAU,WAAW,CAAC,QAAQ,EAAE,YAAY;;;;;;;;;;;;0BAG/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCAAgC;oCAC5C,MAAM,OAAO,CAAC;;;;;;;0CAElB,8OAAC;gCAAI,WAAW,CAAC,4BAA4B,EAAE,YAAY;;kDACzD,8OAAC;wCAAK,WAAU;;4CACb,aAAa,MAAM;4CAAI,OAAO,OAAO,CAAC;;;;;;;kDAEzC,8OAAC;wCAAK,WAAU;;4CAAU;4CACtB,aAAa,MAAM;4CAAI,cAAc,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;oBAKvD,CAAC,WAAW,UAAU,MAAM,mBAC3B,8OAAC;wBAAI,WAAU;;4BACZ,yBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;;4CAAa;4CAAE,QAAQ,OAAO,CAAC;;;;;;;;;;;;;4BAGlD,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;;4CAAa;4CAAE,OAAO,OAAO,CAAC;;;;;;;;;;;;;4BAGjD,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/robot/frontend/src/components/trading/TradingChart.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts';\nimport { Card } from '../ui/Card';\n\ninterface ChartData {\n  time: string;\n  price: number;\n  volume: number;\n}\n\ninterface TradingChartProps {\n  symbol: string;\n  data: ChartData[];\n  timeframe?: string;\n}\n\nexport const TradingChart: React.FC<TradingChartProps> = ({\n  symbol,\n  data,\n  timeframe = '1H'\n}) => {\n  const [chartType, setChartType] = useState<'line' | 'area'>('area');\n\n  const formatPrice = (value: number) => `$${value.toFixed(4)}`;\n  \n  const formatTime = (time: string) => {\n    const date = new Date(time);\n    return date.toLocaleTimeString('en-US', { \n      hour: '2-digit', \n      minute: '2-digit' \n    });\n  };\n\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      return (\n        <div className=\"bg-slate-800 border border-slate-600 rounded-lg p-3 shadow-lg\">\n          <p className=\"text-white font-medium\">{formatTime(label)}</p>\n          <p className=\"text-green-400\">\n            Price: {formatPrice(payload[0].value)}\n          </p>\n          {payload[1] && (\n            <p className=\"text-blue-400\">\n              Volume: {payload[1].value.toLocaleString()}\n            </p>\n          )}\n        </div>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <Card variant=\"chart\" title={`${symbol} Chart`} subtitle={`Timeframe: ${timeframe}`}>\n      <div className=\"mb-4 flex space-x-2\">\n        <button\n          onClick={() => setChartType('area')}\n          className={`px-3 py-1 rounded text-sm ${\n            chartType === 'area' \n              ? 'bg-blue-600 text-white' \n              : 'bg-slate-700 text-gray-300 hover:bg-slate-600'\n          }`}\n        >\n          Area\n        </button>\n        <button\n          onClick={() => setChartType('line')}\n          className={`px-3 py-1 rounded text-sm ${\n            chartType === 'line' \n              ? 'bg-blue-600 text-white' \n              : 'bg-slate-700 text-gray-300 hover:bg-slate-600'\n          }`}\n        >\n          Line\n        </button>\n      </div>\n\n      <div className=\"h-80\">\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          {chartType === 'area' ? (\n            <AreaChart data={data}>\n              <defs>\n                <linearGradient id=\"colorPrice\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n                  <stop offset=\"5%\" stopColor=\"#22c55e\" stopOpacity={0.8}/>\n                  <stop offset=\"95%\" stopColor=\"#22c55e\" stopOpacity={0.1}/>\n                </linearGradient>\n              </defs>\n              <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#374151\" />\n              <XAxis \n                dataKey=\"time\" \n                tickFormatter={formatTime}\n                stroke=\"#9ca3af\"\n                fontSize={12}\n              />\n              <YAxis \n                tickFormatter={formatPrice}\n                stroke=\"#9ca3af\"\n                fontSize={12}\n              />\n              <Tooltip content={<CustomTooltip />} />\n              <Area\n                type=\"monotone\"\n                dataKey=\"price\"\n                stroke=\"#22c55e\"\n                fillOpacity={1}\n                fill=\"url(#colorPrice)\"\n                strokeWidth={2}\n              />\n            </AreaChart>\n          ) : (\n            <LineChart data={data}>\n              <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#374151\" />\n              <XAxis \n                dataKey=\"time\" \n                tickFormatter={formatTime}\n                stroke=\"#9ca3af\"\n                fontSize={12}\n              />\n              <YAxis \n                tickFormatter={formatPrice}\n                stroke=\"#9ca3af\"\n                fontSize={12}\n              />\n              <Tooltip content={<CustomTooltip />} />\n              <Line\n                type=\"monotone\"\n                dataKey=\"price\"\n                stroke=\"#22c55e\"\n                strokeWidth={2}\n                dot={false}\n                activeDot={{ r: 4, fill: '#22c55e' }}\n              />\n            </LineChart>\n          )}\n        </ResponsiveContainer>\n      </div>\n    </Card>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAkBO,MAAM,eAA4C,CAAC,EACxD,MAAM,EACN,IAAI,EACJ,YAAY,IAAI,EACjB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAE5D,MAAM,cAAc,CAAC,QAAkB,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;IAE7D,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA0B,WAAW;;;;;;kCAClD,8OAAC;wBAAE,WAAU;;4BAAiB;4BACpB,YAAY,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;oBAErC,OAAO,CAAC,EAAE,kBACT,8OAAC;wBAAE,WAAU;;4BAAgB;4BAClB,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;;QAKlD;QACA,OAAO;IACT;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,SAAQ;QAAQ,OAAO,GAAG,OAAO,MAAM,CAAC;QAAE,UAAU,CAAC,WAAW,EAAE,WAAW;;0BACjF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,0BAA0B,EACpC,cAAc,SACV,2BACA,iDACJ;kCACH;;;;;;kCAGD,8OAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,0BAA0B,EACpC,cAAc,SACV,2BACA,iDACJ;kCACH;;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACtC,cAAc,uBACb,8OAAC,qJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,8OAAC;0CACC,cAAA,8OAAC;oCAAe,IAAG;oCAAa,IAAG;oCAAI,IAAG;oCAAI,IAAG;oCAAI,IAAG;;sDACtD,8OAAC;4CAAK,QAAO;4CAAK,WAAU;4CAAU,aAAa;;;;;;sDACnD,8OAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAa;;;;;;;;;;;;;;;;;0CAGxD,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,QAAO;;;;;;0CAC5C,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,eAAe;gCACf,QAAO;gCACP,UAAU;;;;;;0CAEZ,8OAAC,qJAAA,CAAA,QAAK;gCACJ,eAAe;gCACf,QAAO;gCACP,UAAU;;;;;;0CAEZ,8OAAC,uJAAA,CAAA,UAAO;gCAAC,uBAAS,8OAAC;;;;;;;;;;0CACnB,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,MAAK;gCACL,aAAa;;;;;;;;;;;6CAIjB,8OAAC,qJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,QAAO;;;;;;0CAC5C,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,eAAe;gCACf,QAAO;gCACP,UAAU;;;;;;0CAEZ,8OAAC,qJAAA,CAAA,QAAK;gCACJ,eAAe;gCACf,QAAO;gCACP,UAAU;;;;;;0CAEZ,8OAAC,uJAAA,CAAA,UAAO;gCAAC,uBAAS,8OAAC;;;;;;;;;;0CACnB,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;gCACL,WAAW;oCAAE,GAAG;oCAAG,MAAM;gCAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/robot/frontend/src/components/dashboard/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { PriceCard } from '../trading/PriceCard';\nimport { TradingChart } from '../trading/TradingChart';\nimport { Card } from '../ui/Card';\nimport { \n  ArrowUpIcon, \n  ArrowDownIcon, \n  CurrencyDollarIcon,\n  ChartBarIcon,\n  BellIcon\n} from '@heroicons/react/24/outline';\n\n// Mock data - في التطبيق الحقيقي سيتم جلب هذه البيانات من API\nconst mockPrices = [\n  {\n    symbol: 'EUR/USD',\n    name: 'Euro / US Dollar',\n    price: 1.0856,\n    change: 0.0023,\n    changePercent: 0.21,\n    volume: '2.1B',\n    high24h: 1.0890,\n    low24h: 1.0820\n  },\n  {\n    symbol: 'GBP/USD',\n    name: 'British Pound / US Dollar',\n    price: 1.2734,\n    change: -0.0045,\n    changePercent: -0.35,\n    volume: '1.8B',\n    high24h: 1.2780,\n    low24h: 1.2710\n  },\n  {\n    symbol: 'USD/JPY',\n    name: 'US Dollar / Japanese Yen',\n    price: 149.85,\n    change: 0.67,\n    changePercent: 0.45,\n    volume: '1.5B',\n    high24h: 150.20,\n    low24h: 149.10\n  },\n  {\n    symbol: 'XAU/USD',\n    name: 'Gold / US Dollar',\n    price: 2034.50,\n    change: 12.30,\n    changePercent: 0.61,\n    volume: '890M',\n    high24h: 2045.80,\n    low24h: 2018.90\n  }\n];\n\nconst mockChartData = Array.from({ length: 24 }, (_, i) => ({\n  time: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),\n  price: 1.0856 + (Math.random() - 0.5) * 0.01,\n  volume: Math.floor(Math.random() * 1000000) + 500000\n}));\n\nconst mockSignals = [\n  {\n    id: 1,\n    symbol: 'EUR/USD',\n    type: 'BUY',\n    confidence: 85,\n    entry: 1.0850,\n    target: 1.0890,\n    stopLoss: 1.0820,\n    time: '2 minutes ago'\n  },\n  {\n    id: 2,\n    symbol: 'GBP/USD',\n    type: 'SELL',\n    confidence: 78,\n    entry: 1.2740,\n    target: 1.2700,\n    stopLoss: 1.2780,\n    time: '15 minutes ago'\n  },\n  {\n    id: 3,\n    symbol: 'XAU/USD',\n    type: 'BUY',\n    confidence: 92,\n    entry: 2030.00,\n    target: 2050.00,\n    stopLoss: 2015.00,\n    time: '1 hour ago'\n  }\n];\n\nexport const Dashboard: React.FC = () => {\n  const [selectedSymbol, setSelectedSymbol] = useState('EUR/USD');\n\n  const stats = [\n    {\n      title: 'Total Portfolio',\n      value: '$125,430.50',\n      change: '+2.4%',\n      isPositive: true,\n      icon: CurrencyDollarIcon\n    },\n    {\n      title: 'Daily P&L',\n      value: '+$2,340.80',\n      change: '+1.9%',\n      isPositive: true,\n      icon: ArrowUpIcon\n    },\n    {\n      title: 'Active Signals',\n      value: '12',\n      change: '+3',\n      isPositive: true,\n      icon: BellIcon\n    },\n    {\n      title: 'Win Rate',\n      value: '78.5%',\n      change: '+2.1%',\n      isPositive: true,\n      icon: ChartBarIcon\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {stats.map((stat, index) => (\n          <Card key={index} variant=\"glass\" className=\"hover:scale-105 transition-transform duration-200\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-400\">{stat.title}</p>\n                <p className=\"text-2xl font-bold text-white mt-1\">{stat.value}</p>\n                <div className={`flex items-center mt-2 ${stat.isPositive ? 'text-green-400' : 'text-red-400'}`}>\n                  {stat.isPositive ? (\n                    <ArrowUpIcon className=\"w-4 h-4 mr-1\" />\n                  ) : (\n                    <ArrowDownIcon className=\"w-4 h-4 mr-1\" />\n                  )}\n                  <span className=\"text-sm\">{stat.change}</span>\n                </div>\n              </div>\n              <div className=\"p-3 bg-blue-500/20 rounded-lg\">\n                <stat.icon className=\"w-6 h-6 text-blue-400\" />\n              </div>\n            </div>\n          </Card>\n        ))}\n      </div>\n\n      {/* Main Content */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Chart */}\n        <div className=\"lg:col-span-2\">\n          <TradingChart \n            symbol={selectedSymbol}\n            data={mockChartData}\n            timeframe=\"1H\"\n          />\n        </div>\n\n        {/* Trading Signals */}\n        <div>\n          <Card title=\"Live Trading Signals\" subtitle=\"AI-powered recommendations\">\n            <div className=\"space-y-4\">\n              {mockSignals.map((signal) => (\n                <div key={signal.id} className=\"p-4 bg-slate-700/50 rounded-lg border border-slate-600\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className=\"font-medium text-white\">{signal.symbol}</span>\n                    <span className={`px-2 py-1 rounded text-xs font-medium ${\n                      signal.type === 'BUY' \n                        ? 'bg-green-500/20 text-green-400' \n                        : 'bg-red-500/20 text-red-400'\n                    }`}>\n                      {signal.type}\n                    </span>\n                  </div>\n                  <div className=\"space-y-1 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-400\">Confidence:</span>\n                      <span className=\"text-white\">{signal.confidence}%</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-400\">Entry:</span>\n                      <span className=\"text-white\">{signal.entry}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-400\">Target:</span>\n                      <span className=\"text-green-400\">{signal.target}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-400\">Stop Loss:</span>\n                      <span className=\"text-red-400\">{signal.stopLoss}</span>\n                    </div>\n                  </div>\n                  <div className=\"mt-2 text-xs text-gray-500\">{signal.time}</div>\n                </div>\n              ))}\n            </div>\n          </Card>\n        </div>\n      </div>\n\n      {/* Price Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {mockPrices.map((price, index) => (\n          <div key={index} onClick={() => setSelectedSymbol(price.symbol)} className=\"cursor-pointer\">\n            <PriceCard {...price} />\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAcA,8DAA8D;AAC9D,MAAM,aAAa;IACjB;QACE,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;QACR,eAAe;QACf,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ,CAAC;QACT,eAAe,CAAC;QAChB,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;QACR,eAAe;QACf,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;QACR,eAAe;QACf,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;CACD;AAED,MAAM,gBAAgB,MAAM,IAAI,CAAC;IAAE,QAAQ;AAAG,GAAG,CAAC,GAAG,IAAM,CAAC;QAC1D,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,MAAM,WAAW;QAClE,OAAO,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QACxC,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW;IAChD,CAAC;AAED,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,MAAM;IACR;CACD;AAEM,MAAM,YAAsB;IACjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM,mOAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM,qNAAA,CAAA,cAAW;QACnB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM,+MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM,uNAAA,CAAA,eAAY;QACpB;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,gIAAA,CAAA,OAAI;wBAAa,SAAQ;wBAAQ,WAAU;kCAC1C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAyB,KAAK,KAAK;;;;;;sDAChD,8OAAC;4CAAE,WAAU;sDAAsC,KAAK,KAAK;;;;;;sDAC7D,8OAAC;4CAAI,WAAW,CAAC,uBAAuB,EAAE,KAAK,UAAU,GAAG,mBAAmB,gBAAgB;;gDAC5F,KAAK,UAAU,iBACd,8OAAC,qNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,8OAAC,yNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DAE3B,8OAAC;oDAAK,WAAU;8DAAW,KAAK,MAAM;;;;;;;;;;;;;;;;;;8CAG1C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;uBAfhB;;;;;;;;;;0BAuBf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6IAAA,CAAA,eAAY;4BACX,QAAQ;4BACR,MAAM;4BACN,WAAU;;;;;;;;;;;kCAKd,8OAAC;kCACC,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,OAAM;4BAAuB,UAAS;sCAC1C,cAAA,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA0B,OAAO,MAAM;;;;;;kEACvD,8OAAC;wDAAK,WAAW,CAAC,sCAAsC,EACtD,OAAO,IAAI,KAAK,QACZ,mCACA,8BACJ;kEACC,OAAO,IAAI;;;;;;;;;;;;0DAGhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;;oEAAc,OAAO,UAAU;oEAAC;;;;;;;;;;;;;kEAElD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAc,OAAO,KAAK;;;;;;;;;;;;kEAE5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAkB,OAAO,MAAM;;;;;;;;;;;;kEAEjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAgB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;0DAGnD,8OAAC;gDAAI,WAAU;0DAA8B,OAAO,IAAI;;;;;;;uCA7BhD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BAsC7B,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,8OAAC;wBAAgB,SAAS,IAAM,kBAAkB,MAAM,MAAM;wBAAG,WAAU;kCACzE,cAAA,8OAAC,0IAAA,CAAA,YAAS;4BAAE,GAAG,KAAK;;;;;;uBADZ;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}]}