//+------------------------------------------------------------------+
//|                                        Test_Ultimate_Indicator.mq5 |
//|                                  Copyright 2024, ForexPro Team |
//|                                             https://forexpro.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, ForexPro Team"
#property link      "https://forexpro.com"
#property version   "1.00"
#property description "اختبار مؤشر التداول الشامل"

#property script_show_inputs

//--- معاملات الاختبار
input string TestSymbol = "EURUSD";           // الرمز للاختبار
input ENUM_TIMEFRAMES TestTimeframe = PERIOD_H1; // الإطار الزمني
input int TestBars = 1000;                    // عدد الشموع للاختبار

//+------------------------------------------------------------------+
//| دالة بداية الاختبار                                            |
//+------------------------------------------------------------------+
void OnStart() {
    Print("🚀 بدء اختبار مؤشر التداول الشامل...");
    
    //--- فحص توفر البيانات
    if(!TestDataAvailability()) {
        Print("❌ خطأ: البيانات غير متوفرة للرمز " + TestSymbol);
        return;
    }
    
    //--- اختبار الدوال الأساسية
    TestBasicFunctions();
    
    //--- اختبار تحليل الاستراتيجيات
    TestStrategyAnalysis();
    
    //--- اختبار حساب المستويات
    TestLevelCalculations();
    
    //--- اختبار العرض المرئي
    TestVisualElements();
    
    Print("✅ اكتمل اختبار المؤشر بنجاح!");
    Print("📊 المؤشر جاهز للاستخدام على الشارت");
}

//+------------------------------------------------------------------+
//| فحص توفر البيانات                                              |
//+------------------------------------------------------------------+
bool TestDataAvailability() {
    //--- فحص الرمز
    if(!SymbolSelect(TestSymbol, true)) {
        Print("❌ الرمز " + TestSymbol + " غير متوفر");
        return false;
    }
    
    //--- فحص البيانات التاريخية
    MqlRates rates[];
    int copied = CopyRates(TestSymbol, TestTimeframe, 0, TestBars, rates);
    
    if(copied < TestBars) {
        Print("⚠️ تحذير: تم نسخ " + IntegerToString(copied) + " شمعة فقط من " + IntegerToString(TestBars));
    } else {
        Print("✅ البيانات متوفرة: " + IntegerToString(copied) + " شمعة");
    }
    
    return copied > 100; // نحتاج على الأقل 100 شمعة
}

//+------------------------------------------------------------------+
//| اختبار الدوال الأساسية                                         |
//+------------------------------------------------------------------+
void TestBasicFunctions() {
    Print("🔍 اختبار الدوال الأساسية...");
    
    //--- اختبار حساب المتوسطات
    MqlRates rates[];
    int copied = CopyRates(TestSymbol, TestTimeframe, 0, 50, rates);
    
    if(copied >= 50) {
        //--- حساب متوسط الأسعار
        double avgPrice = 0;
        for(int i = 0; i < 20; i++) {
            avgPrice += rates[i].close;
        }
        avgPrice /= 20;
        
        Print("📊 متوسط آخر 20 شمعة: " + DoubleToString(avgPrice, _Digits));
        
        //--- اختبار حساب المدى
        double highestHigh = rates[0].high;
        double lowestLow = rates[0].low;
        
        for(int i = 1; i < 20; i++) {
            if(rates[i].high > highestHigh) highestHigh = rates[i].high;
            if(rates[i].low < lowestLow) lowestLow = rates[i].low;
        }
        
        double range = highestHigh - lowestLow;
        Print("📏 مدى آخر 20 شمعة: " + DoubleToString(range, _Digits));
        
        Print("✅ الدوال الأساسية تعمل بشكل صحيح");
    } else {
        Print("❌ فشل في اختبار الدوال الأساسية");
    }
}

//+------------------------------------------------------------------+
//| اختبار تحليل الاستراتيجيات                                     |
//+------------------------------------------------------------------+
void TestStrategyAnalysis() {
    Print("🎯 اختبار تحليل الاستراتيجيات...");
    
    MqlRates rates[];
    long volumes[];
    int copied = CopyRates(TestSymbol, TestTimeframe, 0, 100, rates);
    int volCopied = CopyTickVolume(TestSymbol, TestTimeframe, 0, 100, volumes);
    
    if(copied >= 100 && volCopied >= 100) {
        //--- اختبار تحليل SMC
        bool smcSignal = TestSMCAnalysis(rates, volumes);
        Print("📈 تحليل SMC: " + (smcSignal ? "إشارة موجودة" : "لا توجد إشارة"));
        
        //--- اختبار تحليل SK
        bool skSignal = TestSKAnalysis(rates);
        Print("🎯 تحليل SK: " + (skSignal ? "مستوى مهم" : "لا يوجد مستوى"));
        
        //--- اختبار تحليل PAA
        bool paaSignal = TestPAAAnalysis(rates);
        Print("📊 تحليل PAA: " + (paaSignal ? "نمط قوي" : "لا يوجد نمط"));
        
        //--- اختبار تحليل الحجم
        bool volumeSignal = TestVolumeAnalysis(volumes);
        Print("📈 تحليل الحجم: " + (volumeSignal ? "حجم عالي" : "حجم عادي"));
        
        Print("✅ جميع تحليلات الاستراتيجيات تعمل");
    } else {
        Print("❌ فشل في اختبار تحليل الاستراتيجيات");
    }
}

//+------------------------------------------------------------------+
//| اختبار تحليل SMC                                               |
//+------------------------------------------------------------------+
bool TestSMCAnalysis(MqlRates &rates[], long &volumes[]) {
    int size = ArraySize(rates);
    if(size < 20) return false;
    
    //--- البحث عن Order Block قوي
    for(int i = 10; i < size - 5; i++) {
        double currentBody = MathAbs(rates[i].close - rates[i].open);
        double avgBody = 0;
        
        for(int j = i - 10; j < i; j++) {
            avgBody += MathAbs(rates[j].close - rates[j].open);
        }
        avgBody /= 10;
        
        //--- Order Block قوي
        if(currentBody > avgBody * 2.0 && volumes[i] > volumes[i-1] * 1.5) {
            return true;
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| اختبار تحليل SK                                                |
//+------------------------------------------------------------------+
bool TestSKAnalysis(MqlRates &rates[]) {
    int size = ArraySize(rates);
    if(size < 30) return false;
    
    //--- البحث عن نقاط السوينغ
    for(int i = 15; i < size - 15; i++) {
        //--- فحص السوينغ هاي
        bool isSwingHigh = true;
        for(int j = i - 10; j <= i + 10; j++) {
            if(j != i && rates[j].high >= rates[i].high) {
                isSwingHigh = false;
                break;
            }
        }
        
        if(isSwingHigh) {
            return true; // وجدنا نقطة سوينغ مهمة
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| اختبار تحليل PAA                                               |
//+------------------------------------------------------------------+
bool TestPAAAnalysis(MqlRates &rates[]) {
    int size = ArraySize(rates);
    if(size < 10) return false;
    
    //--- البحث عن أنماط الشموع
    for(int i = 2; i < size - 2; i++) {
        double body = MathAbs(rates[i].close - rates[i].open);
        double range = rates[i].high - rates[i].low;
        double bodyRatio = range > 0 ? body / range : 0;
        
        //--- نمط Hammer
        if(bodyRatio < 0.3 && rates[i].close > (rates[i].high + rates[i].low) / 2) {
            return true;
        }
        
        //--- نمط Engulfing
        double prevBody = MathAbs(rates[i-1].close - rates[i-1].open);
        if(body > prevBody * 1.5) {
            return true;
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| اختبار تحليل الحجم                                             |
//+------------------------------------------------------------------+
bool TestVolumeAnalysis(long &volumes[]) {
    int size = ArraySize(volumes);
    if(size < 20) return false;
    
    //--- حساب متوسط الحجم
    double avgVolume = 0;
    for(int i = size - 20; i < size; i++) {
        avgVolume += volumes[i];
    }
    avgVolume /= 20;
    
    //--- فحص الحجم الحالي
    if(volumes[size-1] > avgVolume * 1.5) {
        return true; // حجم عالي
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| اختبار حساب المستويات                                          |
//+------------------------------------------------------------------+
void TestLevelCalculations() {
    Print("📏 اختبار حساب المستويات...");
    
    //--- اختبار حساب فيبوناتشي
    double high = 1.1000;
    double low = 1.0900;
    double range = high - low;
    
    double fib382 = low + (range * 0.382);
    double fib618 = low + (range * 0.618);
    
    Print("📊 فيبوناتشي 38.2%: " + DoubleToString(fib382, 5));
    Print("📊 فيبوناتشي 61.8%: " + DoubleToString(fib618, 5));
    
    //--- اختبار حساب نسبة المخاطرة
    double entry = 1.0950;
    double stopLoss = 1.0920;
    double riskReward = 3.0;
    double takeProfit = entry + ((entry - stopLoss) * riskReward);
    
    Print("💰 الدخول: " + DoubleToString(entry, 5));
    Print("🛑 الستوب: " + DoubleToString(stopLoss, 5));
    Print("🎯 الهدف: " + DoubleToString(takeProfit, 5));
    Print("📊 نسبة المخاطرة: 1:" + DoubleToString(riskReward, 1));
    
    Print("✅ حسابات المستويات صحيحة");
}

//+------------------------------------------------------------------+
//| اختبار العناصر المرئية                                         |
//+------------------------------------------------------------------+
void TestVisualElements() {
    Print("🎨 اختبار العناصر المرئية...");
    
    //--- اختبار إنشاء مستطيل
    string rectName = "TestRect_" + IntegerToString(GetTickCount());
    datetime startTime = TimeCurrent() - 3600; // ساعة واحدة مضت
    datetime endTime = TimeCurrent();
    double price1 = SymbolInfoDouble(_Symbol, SYMBOL_BID) + 50 * _Point;
    double price2 = SymbolInfoDouble(_Symbol, SYMBOL_BID) + 100 * _Point;
    
    if(ObjectCreate(0, rectName, OBJ_RECTANGLE, 0, startTime, price1, endTime, price2)) {
        ObjectSetInteger(0, rectName, OBJPROP_COLOR, clrLightGreen);
        ObjectSetInteger(0, rectName, OBJPROP_FILL, true);
        Print("✅ تم إنشاء مستطيل اختبار: " + rectName);
        
        //--- حذف المستطيل بعد 5 ثوان
        Sleep(5000);
        ObjectDelete(0, rectName);
        Print("🗑️ تم حذف مستطيل الاختبار");
    } else {
        Print("❌ فشل في إنشاء مستطيل الاختبار");
    }
    
    //--- اختبار إنشاء نص
    string textName = "TestText_" + IntegerToString(GetTickCount());
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    if(ObjectCreate(0, textName, OBJ_TEXT, 0, TimeCurrent(), currentPrice)) {
        ObjectSetString(0, textName, OBJPROP_TEXT, "🎯 اختبار النص العربي");
        ObjectSetInteger(0, textName, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, textName, OBJPROP_FONTSIZE, 12);
        Print("✅ تم إنشاء نص اختبار: " + textName);
        
        //--- حذف النص بعد 5 ثوان
        Sleep(5000);
        ObjectDelete(0, textName);
        Print("🗑️ تم حذف نص الاختبار");
    } else {
        Print("❌ فشل في إنشاء نص الاختبار");
    }
    
    Print("✅ العناصر المرئية تعمل بشكل صحيح");
}
