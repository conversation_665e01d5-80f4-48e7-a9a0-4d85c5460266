{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/robot/frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { \n  ChartBarIcon, \n  BellIcon, \n  UserIcon, \n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\n\nexport const Navbar: React.FC = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [notifications, setNotifications] = useState(3);\n\n  const navItems = [\n    { name: 'Dashboard', href: '/', icon: ChartBarIcon },\n    { name: 'Trading', href: '/trading', icon: ChartBarIcon },\n    { name: 'Signals', href: '/signals', icon: BellIcon },\n    { name: 'Portfolio', href: '/portfolio', icon: UserIcon },\n    { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },\n  ];\n\n  return (\n    <nav className=\"bg-slate-900/95 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center\">\n                <ChartBarIcon className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-white\">ForexPro</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors duration-200\"\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n          </div>\n\n          {/* Right side */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Notifications */}\n            <button className=\"relative p-2 text-gray-300 hover:text-white transition-colors duration-200\">\n              <BellIcon className=\"w-6 h-6\" />\n              {notifications > 0 && (\n                <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                  {notifications}\n                </span>\n              )}\n            </button>\n\n            {/* User Menu */}\n            <div className=\"relative\">\n              <button className=\"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors duration-200\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center\">\n                  <UserIcon className=\"w-4 h-4 text-white\" />\n                </div>\n                <span className=\"hidden sm:block\">John Doe</span>\n              </button>\n            </div>\n\n            {/* Logout */}\n            <button className=\"p-2 text-gray-300 hover:text-red-400 transition-colors duration-200\">\n              <ArrowRightOnRectangleIcon className=\"w-6 h-6\" />\n            </button>\n\n            {/* Mobile menu button */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"md:hidden p-2 text-gray-300 hover:text-white\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"w-6 h-6\" />\n              ) : (\n                <Bars3Icon className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-slate-700\">\n            <div className=\"space-y-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"flex items-center space-x-2 px-3 py-2 text-gray-300 hover:text-white hover:bg-slate-800 rounded-lg transition-colors duration-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <item.icon className=\"w-5 h-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAcO,MAAM,SAAmB;IAC9B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,WAAW;QACf;YAAE,MAAM;YAAa,MAAM;YAAK,MAAM,uNAAA,CAAA,eAAY;QAAC;QACnD;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,uNAAA,CAAA,eAAY;QAAC;QACxD;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,+MAAA,CAAA,WAAQ;QAAC;QACpD;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,+MAAA,CAAA,WAAQ;QAAC;QACxD;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,yNAAA,CAAA,gBAAa;QAAC;KAC5D;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;;;;;;;sCAKnD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,+MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,gBAAgB,mBACf,8OAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;8CAMP,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAK,WAAU;0DAAkB;;;;;;;;;;;;;;;;;8CAKtC,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,iPAAA,CAAA,4BAAyB;wCAAC,WAAU;;;;;;;;;;;8CAIvC,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;;kDAE7B,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAM,KAAK,IAAI;;;;;;;+BANX,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAehC", "debugId": null}}]}