# SMC Liquidity Zones Indicator - دليل الاستخدام

## 🎯 **فكرة المؤشر**

مؤشر متقدم يجمع بين مفاهيم **SMC (Smart Money Concepts)** و **SK (Smart Key Levels)** لتحديد مناطق السيولة القوية وتوليد إشارات تداول دقيقة مع عرض مرئي احترافي للصفقات.

## 🔍 **كيف يعمل المؤشر**

### **1. كشف مناطق السيولة (Order Blocks):**
- يحلل الشموع القوية ذات الحجم العالي
- يحدد مناطق تجمع السيولة (Bullish/Bearish Order Blocks)
- يرسم هذه المناطق كمستطيلات ملونة على الشارت

### **2. تحديد المستويات الرئيسية (SK):**
- يكتشف نقاط السوينغ هاي والسوينغ لو
- يضع مستويات الدعم والمقاومة الرئيسية
- يضيف مستويات فيبوناتشي (38.2% و 61.8%)

### **3. توليد الإشارات:**
- يراقب تفاعل السعر مع Order Blocks
- يتأكد من وجود دعم من المستويات الرئيسية
- يفلتر الإشارات حسب اتجاه الترند
- يعرض إشارات واضحة مع مربعات الصفقة

## 📊 **العرض المرئي**

### **🟢 إشارة الشراء:**
```
🟢 TARGET ZONE    ████████ (مستطيل أخضر)
   
   ENTRY ZONE     ████████ (مستطيل أصفر)
   🟢 (سهم شراء)
   
🔴 STOP ZONE      ████████ (مستطيل أحمر)
```

### **🔴 إشارة البيع:**
```
🔴 STOP ZONE      ████████ (مستطيل أحمر)
   
   ENTRY ZONE     ████████ (مستطيل أصفر)
   🔴 (سهم بيع)
   
🟢 TARGET ZONE    ████████ (مستطيل أخضر)
```

### **العناصر المرئية:**
- **🟦 مستطيلات خضراء فاتحة**: Bullish Order Blocks
- **🟪 مستطيلات وردية فاتحة**: Bearish Order Blocks
- **📏 خطوط ذهبية**: المستويات الرئيسية (SK)
- **🟡 مربع أصفر**: منطقة الدخول
- **🔴 مربع أحمر**: منطقة وقف الخسارة
- **🟢 مربع أخضر**: منطقة الهدف

## ⚙️ **الإعدادات**

### **كشف Order Blocks:**
```cpp
OrderBlockPeriod = 10;        // فترة كشف الأوردر بلوك
MinBodySize = 0.6;            // الحد الأدنى لحجم جسم الشمعة (60%)
OrderBlockExtension = 20;     // امتداد الأوردر بلوك (20 شمعة)
```

### **المستويات الرئيسية (SK):**
```cpp
SwingDetectionPeriod = 15;    // فترة كشف السوينغ
UseFibonacci = true;          // استخدام مستويات فيبوناتشي
FibLevel1 = 38.2;             // مستوى فيبوناتشي الأول
FibLevel2 = 61.8;             // مستوى فيبوناتشي الثاني
```

### **إعدادات الصفقات:**
```cpp
RiskRewardRatio = 2.0;        // نسبة المخاطرة للعائد (1:2)
StopLossBuffer = 5;           // مسافة إضافية لوقف الخسارة (5 نقاط)
ShowTradeBoxes = true;        // إظهار مربعات الصفقة
EnableSignals = true;         // تفعيل الإشارات
```

## 🎯 **شروط الإشارات**

### **إشارة الشراء:**
✅ السعر يتفاعل مع Bullish Order Block  
✅ وجود مستوى دعم رئيسي قريب  
✅ الترند العام صاعد (EMA 20)  
✅ السعر أعلى من إغلاق آخر 5 شموع  

### **إشارة البيع:**
✅ السعر يتفاعل مع Bearish Order Block  
✅ وجود مستوى مقاومة رئيسي قريب  
✅ الترند العام هابط (EMA 20)  
✅ السعر أقل من إغلاق آخر 5 شموع  

## 📈 **مثال عملي**

### **صفقة شراء EUR/USD:**
```
🎯 الإعداد:
- Bullish Order Block عند 1.0840-1.0850
- مستوى دعم فيبوناتشي 61.8% عند 1.0845
- الترند صاعد (فوق EMA 20)

📊 الصفقة:
🟡 ENTRY: 1.0845 (داخل Order Block)
🔴 STOP: 1.0835 (تحت Order Block + 5 نقاط)
🟢 TARGET: 1.0865 (نسبة 1:2)
💰 المخاطرة: 10 نقاط | العائد: 20 نقطة
```

## 🔧 **نصائح الاستخدام**

### **✅ أفضل الممارسات:**
1. **استخدم أطر زمنية أعلى** للتأكيد (H1, H4)
2. **انتظر تأكيد الإشارة** بإغلاق الشمعة
3. **تأكد من قوة Order Block** (حجم عالي)
4. **راقب المستويات الرئيسية** للدعم الإضافي
5. **استخدم إدارة المخاطر** دائماً

### **⚠️ تجنب:**
- التداول ضد الترند الرئيسي
- الدخول في Order Blocks ضعيفة
- تجاهل مستويات وقف الخسارة
- التداول بدون تأكيد من المستويات الرئيسية

## 🎨 **تخصيص الألوان**

```cpp
BullishOrderBlockColor = clrLightGreen;   // أوردر بلوك صاعد
BearishOrderBlockColor = clrLightPink;    // أوردر بلوك هابط
EntryZoneColor = clrYellow;               // منطقة الدخول
StopLossColor = clrRed;                   // منطقة وقف الخسارة
TakeProfitColor = clrLime;                // منطقة الهدف
KeyLevelColor = clrGold;                  // المستويات الرئيسية
```

## 📱 **الإشعارات**

المؤشر يدعم:
- ✅ **تنبيهات صوتية** على المنصة
- ✅ **إشعارات البريد الإلكتروني**
- ✅ **إشعارات الهاتف المحمول**

## 🚀 **التثبيت والاستخدام**

### **1. التثبيت:**
```
- انسخ SMC_Liquidity_Zones_Indicator.mq5 إلى:
  MetaTrader 5/MQL5/Indicators/
- اضغط F7 في MetaEditor للتجميع
```

### **2. التطبيق:**
```
- اسحب المؤشر إلى الشارت
- اضبط الإعدادات حسب تفضيلاتك
- ابدأ مراقبة الإشارات
```

### **3. الأطر الزمنية المناسبة:**
- **M15-M30**: للسكالبينغ
- **H1**: للتداول اليومي
- **H4**: للتداول المتوسط المدى

## 🎯 **النتائج المتوقعة**

مع الاستخدام الصحيح، المؤشر يوفر:
- 📈 **إشارات عالية الجودة** مع تأكيد متعدد المستويات
- 🎯 **نسبة مخاطرة محسوبة** (1:2 افتراضياً)
- 📊 **عرض مرئي واضح** لجميع مستويات الصفقة
- ⚡ **تنبيهات فورية** عند ظهور الفرص

المؤشر مصمم للمتداولين الذين يفهمون مفاهيم SMC ويريدون أداة احترافية لتطبيقها! 🎯
