//+------------------------------------------------------------------+
//|                                SMC_Liquidity_Zones_Indicator.mq5 |
//|                                  Copyright 2024, ForexPro Team |
//|                                             https://forexpro.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, ForexPro Team"
#property link      "https://forexpro.com"
#property version   "1.00"
#property description "SMC Liquidity Zones & SK Smart Key Levels Indicator"
#property description "Advanced Order Block Detection with Visual Trade Setups"

#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- Plot settings for signals
#property indicator_label1  "Buy Signal"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  4

#property indicator_label2  "Sell Signal"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  4

//--- Input parameters
input group "=== Order Block Detection ==="
input int OrderBlockPeriod = 10;                     // Order Block Detection Period
input double MinBodySize = 0.6;                      // Minimum Body Size (% of candle)
input int OrderBlockExtension = 20;                  // Order Block Extension (bars)

input group "=== Smart Key Levels (SK) ==="
input int SwingDetectionPeriod = 15;                 // Swing Detection Period
input bool UseFibonacci = true;                      // Use Fibonacci Levels
input double FibLevel1 = 38.2;                       // Fibonacci Level 1
input double FibLevel2 = 61.8;                       // Fibonacci Level 2

input group "=== Trade Setup Parameters ==="
input double RiskRewardRatio = 2.0;                  // Risk:Reward Ratio (1:X)
input int StopLossBuffer = 5;                        // Stop Loss Buffer (points)
input bool ShowTradeBoxes = true;                    // Show Trade Setup Boxes
input bool EnableSignals = true;                     // Enable Trading Signals

input group "=== Visual Settings ==="
input color BullishOrderBlockColor = clrLightGreen;  // Bullish Order Block Color
input color BearishOrderBlockColor = clrLightPink;   // Bearish Order Block Color
input color EntryZoneColor = clrYellow;              // Entry Zone Color
input color StopLossColor = clrRed;                  // Stop Loss Zone Color
input color TakeProfitColor = clrLime;               // Take Profit Zone Color
input color KeyLevelColor = clrGold;                 // Key Level Color

input group "=== Alert Settings ==="
input bool EnableAlerts = true;                      // Enable Alerts
input bool EnableEmailAlerts = false;                // Enable Email Alerts
input bool EnablePushNotifications = false;          // Enable Push Notifications

//--- Indicator buffers
double BuySignalBuffer[];
double SellSignalBuffer[];

//--- Global variables
struct OrderBlock {
    datetime startTime;
    datetime endTime;
    double highPrice;
    double lowPrice;
    bool isBullish;
    bool isValid;
    int strength;
};

struct KeyLevel {
    double price;
    bool isSupport;
    int strength;
    datetime time;
};

struct TradeSetup {
    datetime signalTime;
    string type;           // "BUY" or "SELL"
    double entryPrice;
    double stopLoss;
    double takeProfit;
    double orderBlockHigh;
    double orderBlockLow;
    bool isActive;
};

OrderBlock orderBlocks[];
KeyLevel keyLevels[];
TradeSetup tradeSetups[];

double lastSwingHigh = 0;
double lastSwingLow = 0;
datetime lastSwingHighTime = 0;
datetime lastSwingLowTime = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit() {
    //--- Set indicator buffers
    SetIndexBuffer(0, BuySignalBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SellSignalBuffer, INDICATOR_DATA);
    
    //--- Set arrow codes
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow for buy
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow for sell
    
    //--- Set empty values
    PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    
    //--- Initialize arrays
    ArrayResize(orderBlocks, 0);
    ArrayResize(keyLevels, 0);
    ArrayResize(tradeSetups, 0);
    
    //--- Set indicator name
    IndicatorSetString(INDICATOR_SHORTNAME, "SMC_Liquidity_Zones");
    
    //--- Set precision
    IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
    
    //--- Check for minimum bars
    if(rates_total < OrderBlockPeriod + SwingDetectionPeriod) return(0);
    
    //--- Calculate start position
    int start = MathMax(prev_calculated - 1, SwingDetectionPeriod);
    if(start < SwingDetectionPeriod) start = SwingDetectionPeriod;
    
    //--- Initialize buffers
    for(int i = start; i < rates_total; i++) {
        BuySignalBuffer[i] = EMPTY_VALUE;
        SellSignalBuffer[i] = EMPTY_VALUE;
    }
    
    //--- Main calculation loop
    for(int i = start; i < rates_total - 1; i++) {
        //--- Update swing points for SK levels
        UpdateSwingPoints(i, high, low, time);
        
        //--- Detect and update order blocks
        DetectOrderBlocks(i, open, high, low, close, time, tick_volume);
        
        //--- Update key levels
        UpdateKeyLevels(i, high, low, close, time);
        
        //--- Generate trading signals
        if(EnableSignals) {
            GenerateTradingSignals(i, open, high, low, close, time);
        }
    }
    
    //--- Draw visual elements
    DrawVisualElements();
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Update swing points for SK strategy                             |
//+------------------------------------------------------------------+
void UpdateSwingPoints(int index, const double &high[], const double &low[], const datetime &time[]) {
    if(index < SwingDetectionPeriod || index >= ArraySize(high) - SwingDetectionPeriod) return;
    
    //--- Check for swing high
    bool isSwingHigh = true;
    for(int j = index - SwingDetectionPeriod; j <= index + SwingDetectionPeriod; j++) {
        if(j != index && high[j] >= high[index]) {
            isSwingHigh = false;
            break;
        }
    }
    
    //--- Check for swing low
    bool isSwingLow = true;
    for(int j = index - SwingDetectionPeriod; j <= index + SwingDetectionPeriod; j++) {
        if(j != index && low[j] <= low[index]) {
            isSwingLow = false;
            break;
        }
    }
    
    //--- Update swing points
    if(isSwingHigh) {
        lastSwingHigh = high[index];
        lastSwingHighTime = time[index];
    }
    
    if(isSwingLow) {
        lastSwingLow = low[index];
        lastSwingLowTime = time[index];
    }
}

//+------------------------------------------------------------------+
//| Detect Order Blocks (SMC Liquidity Zones)                      |
//+------------------------------------------------------------------+
void DetectOrderBlocks(int index, const double &open[], const double &high[],
                      const double &low[], const double &close[],
                      const datetime &time[], const long &tick_volume[]) {

    if(index < OrderBlockPeriod) return;

    double currentBody = MathAbs(close[index] - open[index]);
    double currentRange = high[index] - low[index];
    double bodyRatio = currentRange > 0 ? currentBody / currentRange : 0;

    //--- Check for strong bullish order block
    if(close[index] > open[index] && bodyRatio >= MinBodySize) {
        // Check if this is a strong move (volume and size)
        double avgVolume = 0;
        double avgBody = 0;

        for(int j = index - OrderBlockPeriod; j < index; j++) {
            avgVolume += tick_volume[j];
            avgBody += MathAbs(close[j] - open[j]);
        }
        avgVolume /= OrderBlockPeriod;
        avgBody /= OrderBlockPeriod;

        // Strong bullish candle criteria
        if(tick_volume[index] > avgVolume * 1.2 && currentBody > avgBody * 1.5) {
            OrderBlock newOB;
            newOB.startTime = time[index];
            newOB.endTime = time[index] + PeriodSeconds() * OrderBlockExtension;
            newOB.highPrice = high[index];
            newOB.lowPrice = low[index];
            newOB.isBullish = true;
            newOB.isValid = true;
            newOB.strength = (int)(tick_volume[index] / avgVolume * 10);

            int size = ArraySize(orderBlocks);
            ArrayResize(orderBlocks, size + 1);
            orderBlocks[size] = newOB;
        }
    }

    //--- Check for strong bearish order block
    if(close[index] < open[index] && bodyRatio >= MinBodySize) {
        // Check if this is a strong move (volume and size)
        double avgVolume = 0;
        double avgBody = 0;

        for(int j = index - OrderBlockPeriod; j < index; j++) {
            avgVolume += tick_volume[j];
            avgBody += MathAbs(close[j] - open[j]);
        }
        avgVolume /= OrderBlockPeriod;
        avgBody /= OrderBlockPeriod;

        // Strong bearish candle criteria
        if(tick_volume[index] > avgVolume * 1.2 && currentBody > avgBody * 1.5) {
            OrderBlock newOB;
            newOB.startTime = time[index];
            newOB.endTime = time[index] + PeriodSeconds() * OrderBlockExtension;
            newOB.highPrice = high[index];
            newOB.lowPrice = low[index];
            newOB.isBullish = false;
            newOB.isValid = true;
            newOB.strength = (int)(tick_volume[index] / avgVolume * 10);

            int size = ArraySize(orderBlocks);
            ArrayResize(orderBlocks, size + 1);
            orderBlocks[size] = newOB;
        }
    }
}

//+------------------------------------------------------------------+
//| Update Key Levels (SK Strategy)                                 |
//+------------------------------------------------------------------+
void UpdateKeyLevels(int index, const double &high[], const double &low[],
                    const double &close[], const datetime &time[]) {

    //--- Add swing levels as key levels
    if(lastSwingHigh > 0) {
        KeyLevel newLevel;
        newLevel.price = lastSwingHigh;
        newLevel.isSupport = false;  // Resistance
        newLevel.strength = 10;
        newLevel.time = lastSwingHighTime;

        // Check if this level doesn't already exist
        bool exists = false;
        for(int i = 0; i < ArraySize(keyLevels); i++) {
            if(MathAbs(keyLevels[i].price - newLevel.price) <= 3 * _Point) {
                exists = true;
                keyLevels[i].strength++;  // Increase strength
                break;
            }
        }

        if(!exists) {
            int size = ArraySize(keyLevels);
            ArrayResize(keyLevels, size + 1);
            keyLevels[size] = newLevel;
        }
    }

    if(lastSwingLow > 0) {
        KeyLevel newLevel;
        newLevel.price = lastSwingLow;
        newLevel.isSupport = true;   // Support
        newLevel.strength = 10;
        newLevel.time = lastSwingLowTime;

        // Check if this level doesn't already exist
        bool exists = false;
        for(int i = 0; i < ArraySize(keyLevels); i++) {
            if(MathAbs(keyLevels[i].price - newLevel.price) <= 3 * _Point) {
                exists = true;
                keyLevels[i].strength++;  // Increase strength
                break;
            }
        }

        if(!exists) {
            int size = ArraySize(keyLevels);
            ArrayResize(keyLevels, size + 1);
            keyLevels[size] = newLevel;
        }
    }

    //--- Add Fibonacci levels if enabled
    if(UseFibonacci && lastSwingHigh > 0 && lastSwingLow > 0) {
        double range = lastSwingHigh - lastSwingLow;

        // Fibonacci retracement levels
        double fib382 = lastSwingLow + (range * FibLevel1 / 100.0);
        double fib618 = lastSwingLow + (range * FibLevel2 / 100.0);

        // Add as key levels
        KeyLevel fibLevel1, fibLevel2;

        fibLevel1.price = fib382;
        fibLevel1.isSupport = true;
        fibLevel1.strength = 8;
        fibLevel1.time = time[index];

        fibLevel2.price = fib618;
        fibLevel2.isSupport = true;
        fibLevel2.strength = 8;
        fibLevel2.time = time[index];

        // Add to array (simplified - should check for duplicates)
        int size = ArraySize(keyLevels);
        ArrayResize(keyLevels, size + 2);
        keyLevels[size] = fibLevel1;
        keyLevels[size + 1] = fibLevel2;
    }
}

//+------------------------------------------------------------------+
//| Generate Trading Signals                                        |
//+------------------------------------------------------------------+
void GenerateTradingSignals(int index, const double &open[], const double &high[],
                           const double &low[], const double &close[], const datetime &time[]) {

    double currentPrice = close[index];

    //--- Check for bullish setup (BUY signal)
    for(int i = 0; i < ArraySize(orderBlocks); i++) {
        if(!orderBlocks[i].isValid || !orderBlocks[i].isBullish) continue;

        // Check if price is interacting with bullish order block
        if(currentPrice >= orderBlocks[i].lowPrice &&
           currentPrice <= orderBlocks[i].highPrice &&
           time[index] >= orderBlocks[i].startTime &&
           time[index] <= orderBlocks[i].endTime) {

            // Check if there's a key level supporting this move
            bool keyLevelSupport = false;
            for(int j = 0; j < ArraySize(keyLevels); j++) {
                if(keyLevels[j].isSupport &&
                   MathAbs(currentPrice - keyLevels[j].price) <= 10 * _Point) {
                    keyLevelSupport = true;
                    break;
                }
            }

            // Check trend direction (simple trend filter)
            bool trendBullish = IsTrendBullish(index, close);

            if(keyLevelSupport && trendBullish) {
                // Generate BUY signal
                BuySignalBuffer[index] = low[index] - (10 * _Point);

                // Create trade setup
                TradeSetup newSetup;
                newSetup.signalTime = time[index];
                newSetup.type = "BUY";
                newSetup.entryPrice = currentPrice;
                newSetup.stopLoss = orderBlocks[i].lowPrice - (StopLossBuffer * _Point);
                newSetup.takeProfit = currentPrice + ((currentPrice - newSetup.stopLoss) * RiskRewardRatio);
                newSetup.orderBlockHigh = orderBlocks[i].highPrice;
                newSetup.orderBlockLow = orderBlocks[i].lowPrice;
                newSetup.isActive = true;

                int size = ArraySize(tradeSetups);
                ArrayResize(tradeSetups, size + 1);
                tradeSetups[size] = newSetup;

                // Send alert
                if(EnableAlerts) {
                    SendAlert("BUY Signal", "SMC BUY signal at " + DoubleToString(currentPrice, _Digits));
                }

                break; // Only one signal per bar
            }
        }
    }

    //--- Check for bearish setup (SELL signal)
    for(int i = 0; i < ArraySize(orderBlocks); i++) {
        if(!orderBlocks[i].isValid || orderBlocks[i].isBullish) continue;

        // Check if price is interacting with bearish order block
        if(currentPrice >= orderBlocks[i].lowPrice &&
           currentPrice <= orderBlocks[i].highPrice &&
           time[index] >= orderBlocks[i].startTime &&
           time[index] <= orderBlocks[i].endTime) {

            // Check if there's a key level supporting this move
            bool keyLevelResistance = false;
            for(int j = 0; j < ArraySize(keyLevels); j++) {
                if(!keyLevels[j].isSupport &&
                   MathAbs(currentPrice - keyLevels[j].price) <= 10 * _Point) {
                    keyLevelResistance = true;
                    break;
                }
            }

            // Check trend direction
            bool trendBearish = IsTrendBearish(index, close);

            if(keyLevelResistance && trendBearish) {
                // Generate SELL signal
                SellSignalBuffer[index] = high[index] + (10 * _Point);

                // Create trade setup
                TradeSetup newSetup;
                newSetup.signalTime = time[index];
                newSetup.type = "SELL";
                newSetup.entryPrice = currentPrice;
                newSetup.stopLoss = orderBlocks[i].highPrice + (StopLossBuffer * _Point);
                newSetup.takeProfit = currentPrice - ((newSetup.stopLoss - currentPrice) * RiskRewardRatio);
                newSetup.orderBlockHigh = orderBlocks[i].highPrice;
                newSetup.orderBlockLow = orderBlocks[i].lowPrice;
                newSetup.isActive = true;

                int size = ArraySize(tradeSetups);
                ArrayResize(tradeSetups, size + 1);
                tradeSetups[size] = newSetup;

                // Send alert
                if(EnableAlerts) {
                    SendAlert("SELL Signal", "SMC SELL signal at " + DoubleToString(currentPrice, _Digits));
                }

                break; // Only one signal per bar
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check if trend is bullish                                       |
//+------------------------------------------------------------------+
bool IsTrendBullish(int index, const double &close[]) {
    if(index < 20) return false;

    double ema20 = 0;
    for(int i = 0; i < 20; i++) {
        ema20 += close[index - i];
    }
    ema20 /= 20;

    return close[index] > ema20 && close[index] > close[index - 5];
}

//+------------------------------------------------------------------+
//| Check if trend is bearish                                       |
//+------------------------------------------------------------------+
bool IsTrendBearish(int index, const double &close[]) {
    if(index < 20) return false;

    double ema20 = 0;
    for(int i = 0; i < 20; i++) {
        ema20 += close[index - i];
    }
    ema20 /= 20;

    return close[index] < ema20 && close[index] < close[index - 5];
}

//+------------------------------------------------------------------+
//| Draw visual elements on chart                                   |
//+------------------------------------------------------------------+
void DrawVisualElements() {
    //--- Draw Order Blocks
    DrawOrderBlocks();

    //--- Draw Key Levels
    DrawKeyLevels();

    //--- Draw Trade Setup Boxes
    if(ShowTradeBoxes) {
        DrawTradeSetupBoxes();
    }
}

//+------------------------------------------------------------------+
//| Draw Order Blocks (Liquidity Zones)                            |
//+------------------------------------------------------------------+
void DrawOrderBlocks() {
    string prefix = "OrderBlock_";

    for(int i = 0; i < ArraySize(orderBlocks); i++) {
        if(!orderBlocks[i].isValid) continue;

        string objName = prefix + IntegerToString(i);

        // Create rectangle for order block
        ObjectCreate(0, objName, OBJ_RECTANGLE, 0,
                    orderBlocks[i].startTime, orderBlocks[i].lowPrice,
                    orderBlocks[i].endTime, orderBlocks[i].highPrice);

        color blockColor = orderBlocks[i].isBullish ? BullishOrderBlockColor : BearishOrderBlockColor;
        ObjectSetInteger(0, objName, OBJPROP_COLOR, blockColor);
        ObjectSetInteger(0, objName, OBJPROP_FILL, true);
        ObjectSetInteger(0, objName, OBJPROP_BACK, true);
        ObjectSetInteger(0, objName, OBJPROP_WIDTH, 1);
        ObjectSetString(0, objName, OBJPROP_TEXT,
                       (orderBlocks[i].isBullish ? "Bullish OB" : "Bearish OB") +
                       " (Strength: " + IntegerToString(orderBlocks[i].strength) + ")");

        // Add text label
        string labelName = prefix + "Label_" + IntegerToString(i);
        ObjectCreate(0, labelName, OBJ_TEXT, 0, orderBlocks[i].startTime,
                    orderBlocks[i].isBullish ? orderBlocks[i].highPrice : orderBlocks[i].lowPrice);
        ObjectSetString(0, labelName, OBJPROP_TEXT, orderBlocks[i].isBullish ? "Bullish OB" : "Bearish OB");
        ObjectSetInteger(0, labelName, OBJPROP_COLOR, blockColor);
        ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 8);
    }
}

//+------------------------------------------------------------------+
//| Draw Key Levels (SK Strategy)                                   |
//+------------------------------------------------------------------+
void DrawKeyLevels() {
    string prefix = "KeyLevel_";

    for(int i = 0; i < ArraySize(keyLevels); i++) {
        string objName = prefix + IntegerToString(i);

        // Create horizontal line for key level
        ObjectCreate(0, objName, OBJ_HLINE, 0, 0, keyLevels[i].price);
        ObjectSetInteger(0, objName, OBJPROP_COLOR, KeyLevelColor);
        ObjectSetInteger(0, objName, OBJPROP_STYLE, keyLevels[i].isSupport ? STYLE_SOLID : STYLE_DASH);
        ObjectSetInteger(0, objName, OBJPROP_WIDTH, keyLevels[i].strength > 10 ? 2 : 1);
        ObjectSetString(0, objName, OBJPROP_TEXT,
                       (keyLevels[i].isSupport ? "Support" : "Resistance") +
                       " (" + IntegerToString(keyLevels[i].strength) + ")");
    }
}

//+------------------------------------------------------------------+
//| Draw Trade Setup Boxes                                          |
//+------------------------------------------------------------------+
void DrawTradeSetupBoxes() {
    string prefix = "TradeSetup_";

    for(int i = 0; i < ArraySize(tradeSetups); i++) {
        if(!tradeSetups[i].isActive) continue;

        datetime endTime = tradeSetups[i].signalTime + PeriodSeconds() * 30; // Extend 30 bars

        // Draw Entry Zone (Yellow)
        string entryName = prefix + "Entry_" + IntegerToString(i);
        ObjectCreate(0, entryName, OBJ_RECTANGLE, 0,
                    tradeSetups[i].signalTime, tradeSetups[i].orderBlockLow,
                    endTime, tradeSetups[i].orderBlockHigh);
        ObjectSetInteger(0, entryName, OBJPROP_COLOR, EntryZoneColor);
        ObjectSetInteger(0, entryName, OBJPROP_FILL, true);
        ObjectSetInteger(0, entryName, OBJPROP_BACK, true);
        ObjectSetInteger(0, entryName, OBJPROP_WIDTH, 1);
        ObjectSetString(0, entryName, OBJPROP_TEXT, "ENTRY ZONE");

        // Draw Stop Loss Zone (Red)
        string slName = prefix + "SL_" + IntegerToString(i);
        double slHigh, slLow;
        if(tradeSetups[i].type == "BUY") {
            slHigh = tradeSetups[i].stopLoss + (5 * _Point);
            slLow = tradeSetups[i].stopLoss - (5 * _Point);
        } else {
            slHigh = tradeSetups[i].stopLoss + (5 * _Point);
            slLow = tradeSetups[i].stopLoss - (5 * _Point);
        }

        ObjectCreate(0, slName, OBJ_RECTANGLE, 0, tradeSetups[i].signalTime, slLow, endTime, slHigh);
        ObjectSetInteger(0, slName, OBJPROP_COLOR, StopLossColor);
        ObjectSetInteger(0, slName, OBJPROP_FILL, true);
        ObjectSetInteger(0, slName, OBJPROP_BACK, true);
        ObjectSetInteger(0, slName, OBJPROP_WIDTH, 1);
        ObjectSetString(0, slName, OBJPROP_TEXT, "STOP LOSS");

        // Draw Take Profit Zone (Green)
        string tpName = prefix + "TP_" + IntegerToString(i);
        double tpHigh, tpLow;
        tpHigh = tradeSetups[i].takeProfit + (5 * _Point);
        tpLow = tradeSetups[i].takeProfit - (5 * _Point);

        ObjectCreate(0, tpName, OBJ_RECTANGLE, 0, tradeSetups[i].signalTime, tpLow, endTime, tpHigh);
        ObjectSetInteger(0, tpName, OBJPROP_COLOR, TakeProfitColor);
        ObjectSetInteger(0, tpName, OBJPROP_FILL, true);
        ObjectSetInteger(0, tpName, OBJPROP_BACK, true);
        ObjectSetInteger(0, tpName, OBJPROP_WIDTH, 1);
        ObjectSetString(0, tpName, OBJPROP_TEXT, "TAKE PROFIT");

        // Add text labels
        string entryTextName = prefix + "EntryText_" + IntegerToString(i);
        ObjectCreate(0, entryTextName, OBJ_TEXT, 0, endTime, tradeSetups[i].entryPrice);
        ObjectSetString(0, entryTextName, OBJPROP_TEXT, "ENTRY: " + DoubleToString(tradeSetups[i].entryPrice, _Digits));
        ObjectSetInteger(0, entryTextName, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, entryTextName, OBJPROP_FONTSIZE, 9);

        string slTextName = prefix + "SLText_" + IntegerToString(i);
        ObjectCreate(0, slTextName, OBJ_TEXT, 0, endTime, tradeSetups[i].stopLoss);
        ObjectSetString(0, slTextName, OBJPROP_TEXT, "STOP: " + DoubleToString(tradeSetups[i].stopLoss, _Digits));
        ObjectSetInteger(0, slTextName, OBJPROP_COLOR, StopLossColor);
        ObjectSetInteger(0, slTextName, OBJPROP_FONTSIZE, 9);

        string tpTextName = prefix + "TPText_" + IntegerToString(i);
        ObjectCreate(0, tpTextName, OBJ_TEXT, 0, endTime, tradeSetups[i].takeProfit);
        ObjectSetString(0, tpTextName, OBJPROP_TEXT, "TARGET: " + DoubleToString(tradeSetups[i].takeProfit, _Digits));
        ObjectSetInteger(0, tpTextName, OBJPROP_COLOR, TakeProfitColor);
        ObjectSetInteger(0, tpTextName, OBJPROP_FONTSIZE, 9);
    }
}

//+------------------------------------------------------------------+
//| Send alert notification                                         |
//+------------------------------------------------------------------+
void SendAlert(string title, string message) {
    if(EnableAlerts) {
        Alert(title + ": " + message);
    }

    if(EnableEmailAlerts) {
        SendMail(title, message);
    }

    if(EnablePushNotifications) {
        SendNotification(message);
    }
}

//+------------------------------------------------------------------+
//| Cleanup function                                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    //--- Remove all objects created by indicator
    ObjectsDeleteAll(0, "OrderBlock_");
    ObjectsDeleteAll(0, "KeyLevel_");
    ObjectsDeleteAll(0, "TradeSetup_");

    Comment("");
}
