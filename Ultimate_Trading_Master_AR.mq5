//+------------------------------------------------------------------+
//|                                    Ultimate_Trading_Master_AR.mq5 |
//|                                  Copyright 2024, ForexPro Team |
//|                                             https://forexpro.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, ForexPro Team"
#property link      "https://forexpro.com"
#property version   "1.00"
#property description "مؤشر التداول الشامل - يجمع SMC + ICT + SK + PAA + TD + LCAV + HPV"
#property description "صفقات مضمونة عالية الجودة مع عرض مرئي متطور"

#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- إعدادات الرسم
#property indicator_label1  "إشارة شراء"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  5

#property indicator_label2  "إشارة بيع"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  5

//--- المعاملات الرئيسية
input group "=== إعدادات التحليل الشامل ==="
input int AnalysisPeriod = 50;                       // فترة التحليل الشامل
input double MinSignalStrength = 85.0;               // الحد الأدنى لقوة الإشارة (85%)
input bool UseAllStrategies = true;                  // استخدام جميع الاستراتيجيات
input int SignalValidationBars = 3;                  // عدد الشموع للتأكيد

input group "=== إعدادات SMC (مفاهيم الأموال الذكية) ==="
input bool EnableSMC = true;                         // تفعيل تحليل SMC
input int OrderBlockPeriod = 15;                     // فترة كشف Order Blocks
input int LiquidityLookback = 30;                    // فترة البحث عن السيولة

input group "=== إعدادات ICT (منهجية ICT) ==="
input bool EnableICT = true;                         // تفعيل تحليل ICT
input bool UseKillZones = true;                      // استخدام Kill Zones
input bool UsePDArrays = true;                       // استخدام PD Arrays
input int OptimalTradeEntry = 10;                    // نافذة الدخول المثلى

input group "=== إعدادات SK (المستويات الذكية) ==="
input bool EnableSK = true;                          // تفعيل تحليل SK
input bool UseFibonacci = true;                      // استخدام فيبوناتشي
input int SwingPeriod = 20;                          // فترة السوينغ

input group "=== إعدادات PAA (تحليل حركة السعر) ==="
input bool EnablePAA = true;                         // تفعيل تحليل PAA
input bool UseCandlestickPatterns = true;            // استخدام أنماط الشموع
input bool UseSupplyDemand = true;                   // استخدام العرض والطلب

input group "=== إعدادات TD (Tom DeMark) ==="
input bool EnableTD = true;                          // تفعيل تحليل TD
input int TDSequentialPeriod = 13;                   // فترة TD Sequential

input group "=== إعدادات LCAV & HPV (السيولة والحجم) ==="
input bool EnableVolumeAnalysis = true;              // تفعيل تحليل الحجم
input double VolumeThreshold = 1.5;                  // عتبة الحجم المطلوبة

input group "=== إعدادات الصفقة ==="
input double RiskRewardRatio = 3.0;                  // نسبة المخاطرة للعائد (1:3)
input int StopLossBuffer = 10;                       // مسافة إضافية لوقف الخسارة
input bool ShowTradeSetup = true;                    // إظهار إعداد الصفقة
input int TradeDisplayBars = 100;                    // مدة عرض الصفقة

input group "=== الإعدادات المرئية ==="
input color ProfitZoneColor = clrPaleGreen;          // لون منطقة الربح (أخضر)
input color LossZoneColor = clrMistyRose;            // لون منطقة الخسارة (أحمر)
input color EntryLineColor = clrGold;                // لون خط الدخول (ذهبي)
input int TextSize = 12;                             // حجم النص العربي

input group "=== إعدادات التنبيهات ==="
input bool EnableAlerts = true;                      // تفعيل التنبيهات
input bool EnableEmailAlerts = false;                // تنبيهات البريد الإلكتروني
input bool EnablePushNotifications = true;           // إشعارات الهاتف
input bool PlaySoundAlert = true;                    // تشغيل صوت التنبيه

//--- المتغيرات العامة
double BuySignalBuffer[];
double SellSignalBuffer[];

struct UltimateSignal {
    datetime signalTime;
    string direction;           // "شراء" أو "بيع"
    double entryPrice;
    double stopLoss;
    double takeProfit;
    double signalStrength;      // قوة الإشارة من 0-100
    string analysis;            // تحليل مفصل بالعربية
    bool isActive;
    string strategies;          // الاستراتيجيات المستخدمة
};

UltimateSignal currentSignals[];

// متغيرات التحليل
double lastSwingHigh = 0, lastSwingLow = 0;
datetime lastSwingHighTime = 0, lastSwingLowTime = 0;
bool isKillZoneActive = false;
double currentVolumeFactor = 0;

//+------------------------------------------------------------------+
//| تهيئة المؤشر                                                    |
//+------------------------------------------------------------------+
int OnInit() {
    //--- تعيين البفرز
    SetIndexBuffer(0, BuySignalBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SellSignalBuffer, INDICATOR_DATA);
    
    //--- تعيين رموز الأسهم
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // سهم شراء
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // سهم بيع
    
    //--- تعيين القيم الفارغة
    PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    
    //--- تهيئة المصفوفات
    ArrayResize(currentSignals, 0);
    
    //--- تعيين اسم المؤشر
    IndicatorSetString(INDICATOR_SHORTNAME, "مؤشر التداول الشامل");
    
    //--- تعيين الدقة
    IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
    
    //--- رسالة ترحيب
    Comment("🚀 مؤشر التداول الشامل جاهز!\n" +
            "📊 يجمع أقوى 7 استراتيجيات\n" +
            "🎯 صفقات عالية الجودة فقط\n" +
            "⏰ انتظر الإشارات القوية...");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| الدالة الرئيسية للحساب                                         |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
    
    //--- فحص الحد الأدنى للشموع
    if(rates_total < AnalysisPeriod + 50) return(0);
    
    //--- حساب نقطة البداية
    int start = MathMax(prev_calculated - 1, AnalysisPeriod);
    if(start < AnalysisPeriod) start = AnalysisPeriod;
    
    //--- تهيئة البفرز
    for(int i = start; i < rates_total; i++) {
        BuySignalBuffer[i] = EMPTY_VALUE;
        SellSignalBuffer[i] = EMPTY_VALUE;
    }
    
    //--- الحلقة الرئيسية للتحليل
    for(int i = start; i < rates_total - 1; i++) {
        //--- تحديث بيانات التحليل
        UpdateAnalysisData(i, high, low, close, time, tick_volume);
        
        //--- التحليل الشامل
        double signalStrength = PerformUltimateAnalysis(i, open, high, low, close, time, tick_volume);
        
        //--- توليد الإشارات عالية الجودة فقط
        if(signalStrength >= MinSignalStrength) {
            GenerateUltimateSignal(i, open, high, low, close, time, signalStrength);
        }
    }
    
    //--- رسم الصفقات المرئية
    DrawTradeSetups();
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| تحديث بيانات التحليل                                           |
//+------------------------------------------------------------------+
void UpdateAnalysisData(int index, const double &high[], const double &low[], 
                       const double &close[], const datetime &time[], const long &tick_volume[]) {
    
    //--- تحديث نقاط السوينغ (SK)
    if(EnableSK) {
        UpdateSwingPoints(index, high, low, time);
    }
    
    //--- تحديث Kill Zones (ICT)
    if(EnableICT && UseKillZones) {
        UpdateKillZones(time[index]);
    }
    
    //--- تحديث عامل الحجم (LCAV/HPV)
    if(EnableVolumeAnalysis) {
        UpdateVolumeFactor(index, tick_volume);
    }
}

//+------------------------------------------------------------------+
//| تحديث نقاط السوينغ                                             |
//+------------------------------------------------------------------+
void UpdateSwingPoints(int index, const double &high[], const double &low[], const datetime &time[]) {
    if(index < SwingPeriod || index >= ArraySize(high) - SwingPeriod) return;
    
    //--- فحص السوينغ هاي
    bool isSwingHigh = true;
    for(int j = index - SwingPeriod; j <= index + SwingPeriod; j++) {
        if(j != index && high[j] >= high[index]) {
            isSwingHigh = false;
            break;
        }
    }
    
    //--- فحص السوينغ لو
    bool isSwingLow = true;
    for(int j = index - SwingPeriod; j <= index + SwingPeriod; j++) {
        if(j != index && low[j] <= low[index]) {
            isSwingLow = false;
            break;
        }
    }
    
    //--- تحديث النقاط
    if(isSwingHigh) {
        lastSwingHigh = high[index];
        lastSwingHighTime = time[index];
    }
    
    if(isSwingLow) {
        lastSwingLow = low[index];
        lastSwingLowTime = time[index];
    }
}

//+------------------------------------------------------------------+
//| تحديث Kill Zones (ICT)                                         |
//+------------------------------------------------------------------+
void UpdateKillZones(datetime currentTime) {
    MqlDateTime dt;
    TimeToStruct(currentTime, dt);

    //--- London Kill Zone (08:00-10:00 GMT)
    //--- New York Kill Zone (13:00-15:00 GMT)
    int hour = dt.hour;

    isKillZoneActive = (hour >= 8 && hour <= 10) || (hour >= 13 && hour <= 15);
}

//+------------------------------------------------------------------+
//| تحديث عامل الحجم                                               |
//+------------------------------------------------------------------+
void UpdateVolumeFactor(int index, const long &tick_volume[]) {
    if(index < 20) return;

    double avgVolume = 0;
    for(int i = index - 19; i <= index; i++) {
        avgVolume += tick_volume[i];
    }
    avgVolume /= 20;

    currentVolumeFactor = tick_volume[index] / avgVolume;
}

//+------------------------------------------------------------------+
//| التحليل الشامل لجميع الاستراتيجيات                            |
//+------------------------------------------------------------------+
double PerformUltimateAnalysis(int index, const double &open[], const double &high[],
                              const double &low[], const double &close[],
                              const datetime &time[], const long &tick_volume[]) {

    double totalStrength = 0;
    int activeStrategies = 0;

    //--- تحليل SMC (Smart Money Concepts)
    if(EnableSMC) {
        double smcStrength = AnalyzeSMC(index, open, high, low, close, tick_volume);
        totalStrength += smcStrength;
        activeStrategies++;
    }

    //--- تحليل ICT
    if(EnableICT) {
        double ictStrength = AnalyzeICT(index, open, high, low, close, time);
        totalStrength += ictStrength;
        activeStrategies++;
    }

    //--- تحليل SK (Smart Key Levels)
    if(EnableSK) {
        double skStrength = AnalyzeSK(index, high, low, close);
        totalStrength += skStrength;
        activeStrategies++;
    }

    //--- تحليل PAA (Price Action Analysis)
    if(EnablePAA) {
        double paaStrength = AnalyzePAA(index, open, high, low, close);
        totalStrength += paaStrength;
        activeStrategies++;
    }

    //--- تحليل TD (Tom DeMark)
    if(EnableTD) {
        double tdStrength = AnalyzeTD(index, close);
        totalStrength += tdStrength;
        activeStrategies++;
    }

    //--- تحليل الحجم (LCAV & HPV)
    if(EnableVolumeAnalysis) {
        double volumeStrength = AnalyzeVolume(index, tick_volume);
        totalStrength += volumeStrength;
        activeStrategies++;
    }

    //--- حساب القوة الإجمالية
    return activeStrategies > 0 ? totalStrength / activeStrategies : 0;
}

//+------------------------------------------------------------------+
//| تحليل SMC (Smart Money Concepts)                               |
//+------------------------------------------------------------------+
double AnalyzeSMC(int index, const double &open[], const double &high[],
                 const double &low[], const double &close[], const long &tick_volume[]) {

    double strength = 0;

    //--- كشف Order Blocks
    if(index >= OrderBlockPeriod) {
        double currentBody = MathAbs(close[index] - open[index]);
        double avgBody = 0;

        for(int i = index - OrderBlockPeriod; i < index; i++) {
            avgBody += MathAbs(close[i] - open[i]);
        }
        avgBody /= OrderBlockPeriod;

        //--- Order Block قوي
        if(currentBody > avgBody * 2.0 && currentVolumeFactor > VolumeThreshold) {
            strength += 25;
        }
    }

    //--- كشف Liquidity Sweeps
    if(lastSwingHigh > 0 && lastSwingLow > 0) {
        double currentPrice = close[index];

        //--- اختراق السيولة العلوية
        if(currentPrice > lastSwingHigh && close[index-1] <= lastSwingHigh) {
            strength += 20;
        }

        //--- اختراق السيولة السفلية
        if(currentPrice < lastSwingLow && close[index-1] >= lastSwingLow) {
            strength += 20;
        }
    }

    //--- Break of Structure (BOS)
    if(index >= 10) {
        bool bullishBOS = close[index] > high[index-5] && close[index-1] <= high[index-5];
        bool bearishBOS = close[index] < low[index-5] && close[index-1] >= low[index-5];

        if(bullishBOS || bearishBOS) {
            strength += 15;
        }
    }

    return MathMin(strength, 100);
}

//+------------------------------------------------------------------+
//| تحليل ICT                                                       |
//+------------------------------------------------------------------+
double AnalyzeICT(int index, const double &open[], const double &high[],
                 const double &low[], const double &close[], const datetime &time[]) {

    double strength = 0;

    //--- Kill Zone Bonus
    if(UseKillZones && isKillZoneActive) {
        strength += 20;
    }

    //--- PD Arrays (Premium/Discount)
    if(UsePDArrays && lastSwingHigh > 0 && lastSwingLow > 0) {
        double range = lastSwingHigh - lastSwingLow;
        double currentPrice = close[index];
        double priceLevel = (currentPrice - lastSwingLow) / range;

        //--- Discount Zone (0-0.3)
        if(priceLevel <= 0.3) {
            strength += 25;
        }
        //--- Premium Zone (0.7-1.0)
        else if(priceLevel >= 0.7) {
            strength += 25;
        }
    }

    //--- Optimal Trade Entry (OTE)
    if(index >= OptimalTradeEntry) {
        double recentHigh = high[index];
        double recentLow = low[index];

        for(int i = index - OptimalTradeEntry; i <= index; i++) {
            if(high[i] > recentHigh) recentHigh = high[i];
            if(low[i] < recentLow) recentLow = low[i];
        }

        double currentPrice = close[index];
        double midPoint = (recentHigh + recentLow) / 2;

        //--- قرب من نقطة الوسط (OTE)
        if(MathAbs(currentPrice - midPoint) <= (recentHigh - recentLow) * 0.1) {
            strength += 15;
        }
    }

    return MathMin(strength, 100);
}

//+------------------------------------------------------------------+
//| تحليل SK (Smart Key Levels)                                    |
//+------------------------------------------------------------------+
double AnalyzeSK(int index, const double &high[], const double &low[], const double &close[]) {
    double strength = 0;

    //--- تحليل فيبوناتشي
    if(UseFibonacci && lastSwingHigh > 0 && lastSwingLow > 0) {
        double range = lastSwingHigh - lastSwingLow;
        double currentPrice = close[index];

        //--- مستويات فيبوناتشي الرئيسية
        double fib236 = lastSwingLow + (range * 0.236);
        double fib382 = lastSwingLow + (range * 0.382);
        double fib500 = lastSwingLow + (range * 0.500);
        double fib618 = lastSwingLow + (range * 0.618);
        double fib786 = lastSwingLow + (range * 0.786);

        //--- قرب من مستويات فيبوناتشي المهمة
        double tolerance = range * 0.01; // 1% tolerance

        if(MathAbs(currentPrice - fib618) <= tolerance ||
           MathAbs(currentPrice - fib500) <= tolerance ||
           MathAbs(currentPrice - fib382) <= tolerance) {
            strength += 30;
        }

        if(MathAbs(currentPrice - fib786) <= tolerance ||
           MathAbs(currentPrice - fib236) <= tolerance) {
            strength += 20;
        }
    }

    //--- مستويات الدعم والمقاومة
    if(lastSwingHigh > 0 && lastSwingLow > 0) {
        double currentPrice = close[index];
        double tolerance = (lastSwingHigh - lastSwingLow) * 0.005; // 0.5% tolerance

        //--- قرب من مستوى المقاومة
        if(MathAbs(currentPrice - lastSwingHigh) <= tolerance) {
            strength += 25;
        }

        //--- قرب من مستوى الدعم
        if(MathAbs(currentPrice - lastSwingLow) <= tolerance) {
            strength += 25;
        }
    }

    return MathMin(strength, 100);
}

//+------------------------------------------------------------------+
//| تحليل PAA (Price Action Analysis)                              |
//+------------------------------------------------------------------+
double AnalyzePAA(int index, const double &open[], const double &high[],
                 const double &low[], const double &close[]) {
    double strength = 0;

    //--- تحليل أنماط الشموع
    if(UseCandlestickPatterns && index >= 2) {
        //--- Hammer/Doji patterns
        double body = MathAbs(close[index] - open[index]);
        double range = high[index] - low[index];
        double bodyRatio = range > 0 ? body / range : 0;

        //--- Hammer (مطرقة)
        if(bodyRatio < 0.3 && close[index] > (high[index] + low[index]) / 2) {
            strength += 20;
        }

        //--- Shooting Star (نجمة الرماية)
        if(bodyRatio < 0.3 && close[index] < (high[index] + low[index]) / 2) {
            strength += 20;
        }

        //--- Engulfing Pattern (نمط الابتلاع)
        double prevBody = MathAbs(close[index-1] - open[index-1]);
        if(body > prevBody * 1.5) {
            //--- Bullish Engulfing
            if(close[index] > open[index] && close[index-1] < open[index-1] &&
               close[index] > open[index-1] && open[index] < close[index-1]) {
                strength += 25;
            }
            //--- Bearish Engulfing
            else if(close[index] < open[index] && close[index-1] > open[index-1] &&
                    close[index] < open[index-1] && open[index] > close[index-1]) {
                strength += 25;
            }
        }
    }

    //--- تحليل العرض والطلب
    if(UseSupplyDemand && index >= 10) {
        //--- منطقة طلب قوية
        bool strongDemand = true;
        for(int i = index - 5; i <= index; i++) {
            if(close[i] < open[i]) {
                strongDemand = false;
                break;
            }
        }
        if(strongDemand) strength += 20;

        //--- منطقة عرض قوية
        bool strongSupply = true;
        for(int i = index - 5; i <= index; i++) {
            if(close[i] > open[i]) {
                strongSupply = false;
                break;
            }
        }
        if(strongSupply) strength += 20;
    }

    return MathMin(strength, 100);
}

//+------------------------------------------------------------------+
//| تحليل TD (Tom DeMark)                                          |
//+------------------------------------------------------------------+
double AnalyzeTD(int index, const double &close[]) {
    double strength = 0;

    if(index < TDSequentialPeriod) return 0;

    //--- TD Sequential Count
    int buyCount = 0, sellCount = 0;

    //--- حساب TD Buy Setup
    for(int i = index - TDSequentialPeriod + 1; i <= index; i++) {
        if(i >= 4 && close[i] < close[i-4]) {
            buyCount++;
        } else {
            buyCount = 0;
        }
    }

    //--- حساب TD Sell Setup
    for(int i = index - TDSequentialPeriod + 1; i <= index; i++) {
        if(i >= 4 && close[i] > close[i-4]) {
            sellCount++;
        } else {
            sellCount = 0;
        }
    }

    //--- إشارات TD
    if(buyCount >= 9) {
        strength += 30; // إشارة شراء TD
    }

    if(sellCount >= 9) {
        strength += 30; // إشارة بيع TD
    }

    //--- TD Countdown (تأكيد إضافي)
    if(buyCount >= 13 || sellCount >= 13) {
        strength += 20;
    }

    return MathMin(strength, 100);
}

//+------------------------------------------------------------------+
//| تحليل الحجم (LCAV & HPV)                                       |
//+------------------------------------------------------------------+
double AnalyzeVolume(int index, const long &tick_volume[]) {
    double strength = 0;

    //--- تحليل الحجم العالي
    if(currentVolumeFactor >= VolumeThreshold) {
        strength += 25;

        //--- حجم استثنائي
        if(currentVolumeFactor >= VolumeThreshold * 2) {
            strength += 25;
        }
    }

    //--- تحليل اتجاه الحجم
    if(index >= 5) {
        double volumeTrend = 0;
        for(int i = index - 4; i <= index; i++) {
            if(i > index - 4) {
                if(tick_volume[i] > tick_volume[i-1]) volumeTrend++;
                else volumeTrend--;
            }
        }

        //--- اتجاه حجم متزايد
        if(volumeTrend >= 3) {
            strength += 20;
        }
    }

    //--- Volume Profile Analysis
    if(index >= 20) {
        long maxVolume = 0;
        for(int i = index - 19; i <= index; i++) {
            if(tick_volume[i] > maxVolume) {
                maxVolume = tick_volume[i];
            }
        }

        //--- الحجم الحالي هو الأعلى
        if(tick_volume[index] == maxVolume) {
            strength += 30;
        }
    }

    return MathMin(strength, 100);
}

//+------------------------------------------------------------------+
//| توليد الإشارة النهائية                                         |
//+------------------------------------------------------------------+
void GenerateUltimateSignal(int index, const double &open[], const double &high[],
                           const double &low[], const double &close[],
                           const datetime &time[], double signalStrength) {

    //--- تحديد اتجاه الإشارة
    string direction = DetermineSignalDirection(index, open, high, low, close);
    if(direction == "") return; // لا توجد إشارة واضحة

    //--- حساب مستويات الصفقة
    double entryPrice = close[index];
    double stopLoss = 0, takeProfit = 0;

    if(direction == "شراء") {
        //--- حساب وقف الخسارة للشراء
        stopLoss = CalculateStopLoss(index, low, false);
        //--- حساب الهدف للشراء
        takeProfit = entryPrice + ((entryPrice - stopLoss) * RiskRewardRatio);

        //--- عرض السهم
        BuySignalBuffer[index] = low[index] - (20 * _Point);
    }
    else if(direction == "بيع") {
        //--- حساب وقف الخسارة للبيع
        stopLoss = CalculateStopLoss(index, high, true);
        //--- حساب الهدف للبيع
        takeProfit = entryPrice - ((stopLoss - entryPrice) * RiskRewardRatio);

        //--- عرض السهم
        SellSignalBuffer[index] = high[index] + (20 * _Point);
    }

    //--- إنشاء الإشارة
    UltimateSignal newSignal;
    newSignal.signalTime = time[index];
    newSignal.direction = direction;
    newSignal.entryPrice = entryPrice;
    newSignal.stopLoss = stopLoss;
    newSignal.takeProfit = takeProfit;
    newSignal.signalStrength = signalStrength;
    newSignal.analysis = GenerateAnalysisText(signalStrength, direction);
    newSignal.isActive = true;
    newSignal.strategies = GetActiveStrategies();

    //--- إضافة الإشارة للمصفوفة
    int size = ArraySize(currentSignals);
    ArrayResize(currentSignals, size + 1);
    currentSignals[size] = newSignal;

    //--- إرسال التنبيهات
    SendUltimateAlert(newSignal);
}

//+------------------------------------------------------------------+
//| تحديد اتجاه الإشارة                                            |
//+------------------------------------------------------------------+
string DetermineSignalDirection(int index, const double &open[], const double &high[],
                               const double &low[], const double &close[]) {

    int bullishSignals = 0, bearishSignals = 0;

    //--- تحليل الاتجاه العام
    if(index >= 20) {
        double ema20 = 0;
        for(int i = 0; i < 20; i++) {
            ema20 += close[index - i];
        }
        ema20 /= 20;

        if(close[index] > ema20) bullishSignals++;
        else bearishSignals++;
    }

    //--- تحليل الشمعة الحالية
    if(close[index] > open[index]) bullishSignals++;
    else bearishSignals++;

    //--- تحليل الزخم
    if(index >= 5) {
        if(close[index] > close[index-5]) bullishSignals++;
        else bearishSignals++;
    }

    //--- تحليل الحجم
    if(currentVolumeFactor > VolumeThreshold) {
        if(close[index] > open[index]) bullishSignals++;
        else bearishSignals++;
    }

    //--- تحديد الاتجاه النهائي
    if(bullishSignals > bearishSignals + 1) return "شراء";
    else if(bearishSignals > bullishSignals + 1) return "بيع";

    return ""; // لا توجد إشارة واضحة
}

//+------------------------------------------------------------------+
//| حساب وقف الخسارة                                               |
//+------------------------------------------------------------------+
double CalculateStopLoss(int index, const double &prices[], bool isHigh) {
    double extremePrice = prices[index];

    //--- البحث عن أقصى/أدنى سعر في آخر 20 شمعة
    for(int i = index - 19; i <= index; i++) {
        if(i >= 0) {
            if(isHigh) {
                if(prices[i] > extremePrice) extremePrice = prices[i];
            } else {
                if(prices[i] < extremePrice) extremePrice = prices[i];
            }
        }
    }

    //--- إضافة مسافة أمان
    if(isHigh) {
        return extremePrice + (StopLossBuffer * _Point);
    } else {
        return extremePrice - (StopLossBuffer * _Point);
    }
}

//+------------------------------------------------------------------+
//| توليد نص التحليل بالعربية                                      |
//+------------------------------------------------------------------+
string GenerateAnalysisText(double strength, string direction) {
    string analysis = "🎯 إشارة " + direction + " قوية\n";
    analysis += "📊 قوة الإشارة: " + DoubleToString(strength, 1) + "%\n";

    if(strength >= 95) {
        analysis += "⭐ إشارة استثنائية - جودة عالية جداً\n";
    } else if(strength >= 90) {
        analysis += "🔥 إشارة ممتازة - جودة عالية\n";
    } else {
        analysis += "✅ إشارة جيدة - جودة مقبولة\n";
    }

    analysis += "📈 الاستراتيجيات المؤكدة:\n";

    if(EnableSMC) analysis += "• SMC (مفاهيم الأموال الذكية)\n";
    if(EnableICT) analysis += "• ICT (منهجية ICT)\n";
    if(EnableSK) analysis += "• SK (المستويات الذكية)\n";
    if(EnablePAA) analysis += "• PAA (تحليل حركة السعر)\n";
    if(EnableTD) analysis += "• TD (Tom DeMark)\n";
    if(EnableVolumeAnalysis) analysis += "• تحليل الحجم المتقدم\n";

    return analysis;
}

//+------------------------------------------------------------------+
//| الحصول على الاستراتيجيات النشطة                               |
//+------------------------------------------------------------------+
string GetActiveStrategies() {
    string strategies = "";

    if(EnableSMC) strategies += "SMC+";
    if(EnableICT) strategies += "ICT+";
    if(EnableSK) strategies += "SK+";
    if(EnablePAA) strategies += "PAA+";
    if(EnableTD) strategies += "TD+";
    if(EnableVolumeAnalysis) strategies += "VOL+";

    if(StringLen(strategies) > 0) {
        strategies = StringSubstr(strategies, 0, StringLen(strategies) - 1); // إزالة آخر +
    }

    return strategies;
}

//+------------------------------------------------------------------+
//| رسم إعدادات الصفقات المرئية                                    |
//+------------------------------------------------------------------+
void DrawTradeSetups() {
    if(!ShowTradeSetup) return;

    string prefix = "UltimateSignal_";

    for(int i = 0; i < ArraySize(currentSignals); i++) {
        if(!currentSignals[i].isActive) continue;

        datetime startTime = currentSignals[i].signalTime;
        datetime endTime = startTime + PeriodSeconds() * TradeDisplayBars;

        //--- رسم المناطق حسب نوع الصفقة
        if(currentSignals[i].direction == "شراء") {
            //--- منطقة الربح (أخضر) - فوق الدخول
            string profitZoneName = prefix + "Profit_" + IntegerToString(i);
            ObjectCreate(0, profitZoneName, OBJ_RECTANGLE, 0,
                        startTime, currentSignals[i].entryPrice,
                        endTime, currentSignals[i].takeProfit);
            ObjectSetInteger(0, profitZoneName, OBJPROP_COLOR, ProfitZoneColor);
            ObjectSetInteger(0, profitZoneName, OBJPROP_FILL, true);
            ObjectSetInteger(0, profitZoneName, OBJPROP_BACK, true);
            ObjectSetInteger(0, profitZoneName, OBJPROP_WIDTH, 1);

            //--- منطقة الخسارة (أحمر) - تحت الدخول
            string lossZoneName = prefix + "Loss_" + IntegerToString(i);
            ObjectCreate(0, lossZoneName, OBJ_RECTANGLE, 0,
                        startTime, currentSignals[i].stopLoss,
                        endTime, currentSignals[i].entryPrice);
            ObjectSetInteger(0, lossZoneName, OBJPROP_COLOR, LossZoneColor);
            ObjectSetInteger(0, lossZoneName, OBJPROP_FILL, true);
            ObjectSetInteger(0, lossZoneName, OBJPROP_BACK, true);
            ObjectSetInteger(0, lossZoneName, OBJPROP_WIDTH, 1);
        }
        else if(currentSignals[i].direction == "بيع") {
            //--- منطقة الخسارة (أحمر) - فوق الدخول
            string lossZoneName = prefix + "Loss_" + IntegerToString(i);
            ObjectCreate(0, lossZoneName, OBJ_RECTANGLE, 0,
                        startTime, currentSignals[i].entryPrice,
                        endTime, currentSignals[i].stopLoss);
            ObjectSetInteger(0, lossZoneName, OBJPROP_COLOR, LossZoneColor);
            ObjectSetInteger(0, lossZoneName, OBJPROP_FILL, true);
            ObjectSetInteger(0, lossZoneName, OBJPROP_BACK, true);
            ObjectSetInteger(0, lossZoneName, OBJPROP_WIDTH, 1);

            //--- منطقة الربح (أخضر) - تحت الدخول
            string profitZoneName = prefix + "Profit_" + IntegerToString(i);
            ObjectCreate(0, profitZoneName, OBJ_RECTANGLE, 0,
                        startTime, currentSignals[i].takeProfit,
                        endTime, currentSignals[i].entryPrice);
            ObjectSetInteger(0, profitZoneName, OBJPROP_COLOR, ProfitZoneColor);
            ObjectSetInteger(0, profitZoneName, OBJPROP_FILL, true);
            ObjectSetInteger(0, profitZoneName, OBJPROP_BACK, true);
            ObjectSetInteger(0, profitZoneName, OBJPROP_WIDTH, 1);
        }

        //--- خط الدخول (ذهبي)
        string entryLineName = prefix + "Entry_" + IntegerToString(i);
        ObjectCreate(0, entryLineName, OBJ_TREND, 0,
                    startTime, currentSignals[i].entryPrice,
                    endTime, currentSignals[i].entryPrice);
        ObjectSetInteger(0, entryLineName, OBJPROP_COLOR, EntryLineColor);
        ObjectSetInteger(0, entryLineName, OBJPROP_WIDTH, 3);
        ObjectSetInteger(0, entryLineName, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, entryLineName, OBJPROP_RAY_RIGHT, false);

        //--- النصوص العربية
        string entryTextName = prefix + "EntryText_" + IntegerToString(i);
        ObjectCreate(0, entryTextName, OBJ_TEXT, 0,
                    endTime + PeriodSeconds() * 3, currentSignals[i].entryPrice);
        ObjectSetString(0, entryTextName, OBJPROP_TEXT,
                       "دخول: " + DoubleToString(currentSignals[i].entryPrice, _Digits));
        ObjectSetInteger(0, entryTextName, OBJPROP_COLOR, EntryLineColor);
        ObjectSetInteger(0, entryTextName, OBJPROP_FONTSIZE, TextSize);
        ObjectSetInteger(0, entryTextName, OBJPROP_ANCHOR, ANCHOR_LEFT);

        string tpTextName = prefix + "TPText_" + IntegerToString(i);
        ObjectCreate(0, tpTextName, OBJ_TEXT, 0,
                    endTime + PeriodSeconds() * 3, currentSignals[i].takeProfit);
        ObjectSetString(0, tpTextName, OBJPROP_TEXT,
                       "هدف: " + DoubleToString(currentSignals[i].takeProfit, _Digits));
        ObjectSetInteger(0, tpTextName, OBJPROP_COLOR, clrDarkGreen);
        ObjectSetInteger(0, tpTextName, OBJPROP_FONTSIZE, TextSize);
        ObjectSetInteger(0, tpTextName, OBJPROP_ANCHOR, ANCHOR_LEFT);

        string slTextName = prefix + "SLText_" + IntegerToString(i);
        ObjectCreate(0, slTextName, OBJ_TEXT, 0,
                    endTime + PeriodSeconds() * 3, currentSignals[i].stopLoss);
        ObjectSetString(0, slTextName, OBJPROP_TEXT,
                       "ستوب: " + DoubleToString(currentSignals[i].stopLoss, _Digits));
        ObjectSetInteger(0, slTextName, OBJPROP_COLOR, clrDarkRed);
        ObjectSetInteger(0, slTextName, OBJPROP_FONTSIZE, TextSize);
        ObjectSetInteger(0, slTextName, OBJPROP_ANCHOR, ANCHOR_LEFT);

        //--- معلومات الإشارة
        string infoTextName = prefix + "Info_" + IntegerToString(i);
        ObjectCreate(0, infoTextName, OBJ_TEXT, 0,
                    startTime - PeriodSeconds() * 5, currentSignals[i].entryPrice);
        ObjectSetString(0, infoTextName, OBJPROP_TEXT,
                       "🎯 " + currentSignals[i].direction + " | " +
                       DoubleToString(currentSignals[i].signalStrength, 1) + "% | " +
                       currentSignals[i].strategies);
        ObjectSetInteger(0, infoTextName, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, infoTextName, OBJPROP_FONTSIZE, TextSize + 2);
        ObjectSetInteger(0, infoTextName, OBJPROP_ANCHOR, ANCHOR_RIGHT);
    }
}

//+------------------------------------------------------------------+
//| إرسال التنبيهات الشاملة                                        |
//+------------------------------------------------------------------+
void SendUltimateAlert(UltimateSignal &signal) {
    string alertTitle = "🚀 إشارة تداول شاملة - " + signal.direction;
    string alertMessage = signal.analysis + "\n" +
                         "💰 الدخول: " + DoubleToString(signal.entryPrice, _Digits) + "\n" +
                         "🎯 الهدف: " + DoubleToString(signal.takeProfit, _Digits) + "\n" +
                         "🛑 الستوب: " + DoubleToString(signal.stopLoss, _Digits) + "\n" +
                         "📊 نسبة المخاطرة: 1:" + DoubleToString(RiskRewardRatio, 1);

    //--- تنبيه صوتي
    if(EnableAlerts) {
        Alert(alertTitle + "\n" + alertMessage);
    }

    //--- صوت التنبيه
    if(PlaySoundAlert) {
        if(signal.direction == "شراء") {
            PlaySound("alert.wav");
        } else {
            PlaySound("alert2.wav");
        }
    }

    //--- بريد إلكتروني
    if(EnableEmailAlerts) {
        SendMail(alertTitle, alertMessage);
    }

    //--- إشعار الهاتف
    if(EnablePushNotifications) {
        SendNotification(alertTitle + " - " + signal.direction + " " +
                        DoubleToString(signal.signalStrength, 1) + "%");
    }

    //--- تحديث التعليق
    Comment("🎯 آخر إشارة: " + signal.direction + "\n" +
            "📊 القوة: " + DoubleToString(signal.signalStrength, 1) + "%\n" +
            "💰 الدخول: " + DoubleToString(signal.entryPrice, _Digits) + "\n" +
            "🎯 الهدف: " + DoubleToString(signal.takeProfit, _Digits) + "\n" +
            "🛑 الستوب: " + DoubleToString(signal.stopLoss, _Digits) + "\n" +
            "⏰ الوقت: " + TimeToString(signal.signalTime) + "\n" +
            "🔥 الاستراتيجيات: " + signal.strategies);
}

//+------------------------------------------------------------------+
//| تنظيف المؤشر                                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    //--- حذف جميع الكائنات
    ObjectsDeleteAll(0, "UltimateSignal_");

    //--- مسح التعليق
    Comment("");
}
