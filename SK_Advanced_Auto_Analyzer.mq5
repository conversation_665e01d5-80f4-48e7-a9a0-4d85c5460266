//+------------------------------------------------------------------+
//|                                      SK_Advanced_Auto_Analyzer.mq5 |
//|                                  Copyright 2024, ForexPro Team |
//|                                             https://forexpro.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, ForexPro Team"
#property link      "https://forexpro.com"
#property version   "1.00"
#property description "Advanced SK Auto Analyzer - Automatic Chart Analysis"
#property description "Auto Fibonacci, Liquidity Zones, Order Blocks & Trading Signals"

#property indicator_chart_window
#property indicator_buffers 4
#property indicator_plots   4

//--- Plot settings
#property indicator_label1  "Buy Signal"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  4

#property indicator_label2  "Sell Signal"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  4

#property indicator_label3  "Support Level"
#property indicator_type3   DRAW_ARROW
#property indicator_color3  clrBlue
#property indicator_style3  STYLE_SOLID
#property indicator_width3  2

#property indicator_label4  "Resistance Level"
#property indicator_type4   DRAW_ARROW
#property indicator_color4  clrOrange
#property indicator_style4  STYLE_SOLID
#property indicator_width4  2

//--- Input parameters
input group "=== SK Analysis Settings ==="
input int SwingDetectionPeriod = 20;                 // Swing Detection Period
input int FibonacciLookback = 100;                   // Fibonacci Analysis Lookback
input bool AutoDrawFibonacci = true;                 // Auto Draw Fibonacci Levels
input bool ShowFibExtensions = true;                 // Show Fibonacci Extensions

input group "=== Liquidity & Order Blocks ==="
input bool ShowLiquidityZones = true;                // Show Liquidity Zones
input bool ShowOrderBlocks = true;                   // Show Order Blocks
input int OrderBlockPeriod = 15;                     // Order Block Detection Period
input double MinOrderBlockSize = 0.6;                // Minimum Order Block Size (%)

input group "=== Signal Generation ==="
input bool EnableTradingSignals = true;              // Enable Trading Signals
input double MinSignalStrength = 75.0;               // Minimum Signal Strength (%)
input bool PlaySoundAlerts = true;                   // Play Sound Alerts
input bool ShowSignalInfo = true;                    // Show Signal Information

input group "=== Visual Settings ==="
input color FibonacciColor = clrGold;                // Fibonacci Lines Color
input color SupportColor = clrBlue;                  // Support Levels Color
input color ResistanceColor = clrRed;                // Resistance Levels Color
input color LiquidityZoneColor = clrYellow;          // Liquidity Zone Color
input color BullishOrderBlockColor = clrLightGreen; // Bullish Order Block Color
input color BearishOrderBlockColor = clrLightPink;  // Bearish Order Block Color

input group "=== Control Panel ==="
input bool ShowControlPanel = true;                  // Show Control Panel
input int PanelXPosition = 20;                       // Panel X Position
input int PanelYPosition = 50;                       // Panel Y Position
input color PanelBackgroundColor = clrWhite;         // Panel Background Color
input color PanelTextColor = clrBlack;               // Panel Text Color

//--- Indicator buffers
double BuySignalBuffer[];
double SellSignalBuffer[];
double SupportBuffer[];
double ResistanceBuffer[];

//--- Global variables
struct SwingPoint {
    datetime time;
    double price;
    bool isHigh;
    int strength;
};

struct FibonacciLevel {
    double price;
    double percentage;
    string description;
};

struct OrderBlock {
    datetime startTime;
    datetime endTime;
    double highPrice;
    double lowPrice;
    bool isBullish;
    int strength;
};

struct TradingSignal {
    datetime time;
    string type;           // "BUY" or "SELL"
    double price;
    double stopLoss;
    double takeProfit;
    double strength;
    string analysis;
};

SwingPoint swingPoints[];
FibonacciLevel fibLevels[];
OrderBlock orderBlocks[];
TradingSignal currentSignals[];

// Market analysis variables
string currentTrend = "ANALYZING";
double trendStrength = 0.0;
double supportLevel = 0.0;
double resistanceLevel = 0.0;
bool marketStructureBullish = false;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit() {
    //--- Set indicator buffers
    SetIndexBuffer(0, BuySignalBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SellSignalBuffer, INDICATOR_DATA);
    SetIndexBuffer(2, SupportBuffer, INDICATOR_DATA);
    SetIndexBuffer(3, ResistanceBuffer, INDICATOR_DATA);
    
    //--- Set arrow codes
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow for buy
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow for sell
    PlotIndexSetInteger(2, PLOT_ARROW, 159);  // Support arrow
    PlotIndexSetInteger(3, PLOT_ARROW, 159);  // Resistance arrow
    
    //--- Set empty values
    PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    
    //--- Initialize arrays
    ArrayResize(swingPoints, 0);
    ArrayResize(fibLevels, 0);
    ArrayResize(orderBlocks, 0);
    ArrayResize(currentSignals, 0);
    
    //--- Set indicator name
    IndicatorSetString(INDICATOR_SHORTNAME, "SK Advanced Auto Analyzer");
    
    //--- Set precision
    IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
    
    //--- Welcome message
    Print("SK Advanced Auto Analyzer initialized successfully!");
    Print("Auto-analyzing chart for swing points, Fibonacci levels, and trading opportunities...");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
    
    //--- Check for minimum bars
    if(rates_total < SwingDetectionPeriod + FibonacciLookback) return(0);
    
    //--- Calculate start position
    int start = MathMax(prev_calculated - 1, SwingDetectionPeriod);
    if(start < SwingDetectionPeriod) start = SwingDetectionPeriod;
    
    //--- Initialize buffers
    for(int i = start; i < rates_total; i++) {
        BuySignalBuffer[i] = EMPTY_VALUE;
        SellSignalBuffer[i] = EMPTY_VALUE;
        SupportBuffer[i] = EMPTY_VALUE;
        ResistanceBuffer[i] = EMPTY_VALUE;
    }
    
    //--- Main analysis loop
    for(int i = start; i < rates_total - 1; i++) {
        //--- Detect swing points
        DetectSwingPoints(i, high, low, time);
        
        //--- Analyze market structure
        AnalyzeMarketStructure(i, high, low, close);
        
        //--- Detect order blocks
        if(ShowOrderBlocks) {
            DetectOrderBlocks(i, open, high, low, close, time, tick_volume);
        }
        
        //--- Generate trading signals
        if(EnableTradingSignals) {
            GenerateTradingSignals(i, open, high, low, close, time);
        }
    }
    
    //--- Draw Fibonacci levels automatically
    if(AutoDrawFibonacci) {
        DrawAutomaticFibonacci();
    }
    
    //--- Draw liquidity zones
    if(ShowLiquidityZones) {
        DrawLiquidityZones();
    }
    
    //--- Draw order blocks
    if(ShowOrderBlocks) {
        DrawOrderBlocks();
    }
    
    //--- Update control panel
    if(ShowControlPanel) {
        UpdateControlPanel();
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Detect swing points using SK methodology                        |
//+------------------------------------------------------------------+
void DetectSwingPoints(int index, const double &high[], const double &low[], const datetime &time[]) {
    if(index < SwingDetectionPeriod || index >= ArraySize(high) - SwingDetectionPeriod) return;
    
    //--- Check for swing high
    bool isSwingHigh = true;
    int strength = 0;
    
    for(int j = index - SwingDetectionPeriod; j <= index + SwingDetectionPeriod; j++) {
        if(j != index) {
            if(high[j] >= high[index]) {
                isSwingHigh = false;
                break;
            }
            if(high[j] < high[index]) strength++;
        }
    }
    
    //--- Check for swing low
    bool isSwingLow = true;
    int lowStrength = 0;
    
    for(int j = index - SwingDetectionPeriod; j <= index + SwingDetectionPeriod; j++) {
        if(j != index) {
            if(low[j] <= low[index]) {
                isSwingLow = false;
                break;
            }
            if(low[j] > low[index]) lowStrength++;
        }
    }
    
    //--- Add swing high
    if(isSwingHigh && strength >= SwingDetectionPeriod) {
        SwingPoint newSwing;
        newSwing.time = time[index];
        newSwing.price = high[index];
        newSwing.isHigh = true;
        newSwing.strength = strength;
        
        int size = ArraySize(swingPoints);
        ArrayResize(swingPoints, size + 1);
        swingPoints[size] = newSwing;
        
        // Mark resistance level
        ResistanceBuffer[index] = high[index];
        resistanceLevel = high[index];
    }
    
    //--- Add swing low
    if(isSwingLow && lowStrength >= SwingDetectionPeriod) {
        SwingPoint newSwing;
        newSwing.time = time[index];
        newSwing.price = low[index];
        newSwing.isHigh = false;
        newSwing.strength = lowStrength;
        
        int size = ArraySize(swingPoints);
        ArrayResize(swingPoints, size + 1);
        swingPoints[size] = newSwing;
        
        // Mark support level
        SupportBuffer[index] = low[index];
        supportLevel = low[index];
    }
}

//+------------------------------------------------------------------+
//| Analyze market structure for trend determination                |
//+------------------------------------------------------------------+
void AnalyzeMarketStructure(int index, const double &high[], const double &low[], const double &close[]) {
    if(ArraySize(swingPoints) < 4) return;
    
    int swingCount = ArraySize(swingPoints);
    int recentSwings = MathMin(6, swingCount);
    
    int higherHighs = 0;
    int lowerLows = 0;
    int lowerHighs = 0;
    int higherLows = 0;
    
    //--- Analyze recent swing structure
    for(int i = swingCount - recentSwings; i < swingCount - 1; i++) {
        if(i < 0) continue;
        
        if(swingPoints[i].isHigh && swingPoints[i+1].isHigh) {
            if(swingPoints[i+1].price > swingPoints[i].price) {
                higherHighs++;
            } else {
                lowerHighs++;
            }
        }
        
        if(!swingPoints[i].isHigh && !swingPoints[i+1].isHigh) {
            if(swingPoints[i+1].price > swingPoints[i].price) {
                higherLows++;
            } else {
                lowerLows++;
            }
        }
    }
    
    //--- Determine trend
    if(higherHighs >= 2 && higherLows >= 1) {
        currentTrend = "BULLISH";
        marketStructureBullish = true;
        trendStrength = (double)(higherHighs + higherLows) / (double)recentSwings * 100.0;
    } else if(lowerHighs >= 2 && lowerLows >= 1) {
        currentTrend = "BEARISH";
        marketStructureBullish = false;
        trendStrength = (double)(lowerHighs + lowerLows) / (double)recentSwings * 100.0;
    } else {
        currentTrend = "SIDEWAYS";
        marketStructureBullish = false;
        trendStrength = 50.0;
    }
}

//+------------------------------------------------------------------+
//| Detect order blocks using volume and price action              |
//+------------------------------------------------------------------+
void DetectOrderBlocks(int index, const double &open[], const double &high[], 
                      const double &low[], const double &close[], 
                      const datetime &time[], const long &tick_volume[]) {
    
    if(index < OrderBlockPeriod) return;
    
    double currentBody = MathAbs(close[index] - open[index]);
    double currentRange = high[index] - low[index];
    double bodyRatio = currentRange > 0 ? currentBody / currentRange : 0;
    
    //--- Calculate average volume and body size
    double avgVolume = 0;
    double avgBody = 0;
    
    for(int j = index - OrderBlockPeriod; j < index; j++) {
        avgVolume += tick_volume[j];
        avgBody += MathAbs(close[j] - open[j]);
    }
    avgVolume /= OrderBlockPeriod;
    avgBody /= OrderBlockPeriod;
    
    //--- Check for strong bullish order block
    if(close[index] > open[index] && bodyRatio >= MinOrderBlockSize && 
       tick_volume[index] > avgVolume * 1.5 && currentBody > avgBody * 1.8) {
        
        OrderBlock newOB;
        newOB.startTime = time[index];
        newOB.endTime = time[index] + PeriodSeconds() * 50;
        newOB.highPrice = high[index];
        newOB.lowPrice = low[index];
        newOB.isBullish = true;
        newOB.strength = (int)(tick_volume[index] / avgVolume * 10);
        
        int size = ArraySize(orderBlocks);
        ArrayResize(orderBlocks, size + 1);
        orderBlocks[size] = newOB;
    }
    
    //--- Check for strong bearish order block
    if(close[index] < open[index] && bodyRatio >= MinOrderBlockSize && 
       tick_volume[index] > avgVolume * 1.5 && currentBody > avgBody * 1.8) {
        
        OrderBlock newOB;
        newOB.startTime = time[index];
        newOB.endTime = time[index] + PeriodSeconds() * 50;
        newOB.highPrice = high[index];
        newOB.lowPrice = low[index];
        newOB.isBullish = false;
        newOB.strength = (int)(tick_volume[index] / avgVolume * 10);
        
        int size = ArraySize(orderBlocks);
        ArrayResize(orderBlocks, size + 1);
        orderBlocks[size] = newOB;
    }
}

//+------------------------------------------------------------------+
//| Generate trading signals based on SK analysis                   |
//+------------------------------------------------------------------+
void GenerateTradingSignals(int index, const double &open[], const double &high[],
                           const double &low[], const double &close[], const datetime &time[]) {

    double currentPrice = close[index];
    double signalStrength = 0.0;
    string signalType = "";
    string analysis = "";

    //--- Check for bullish signal
    if(marketStructureBullish && currentTrend == "BULLISH") {
        // Price near support level
        if(supportLevel > 0 && MathAbs(currentPrice - supportLevel) <= (supportLevel * 0.002)) {
            signalStrength += 30.0;
            analysis += "Price at strong support level. ";
        }

        // Fibonacci retracement level
        if(IsAtFibonacciLevel(currentPrice, true)) {
            signalStrength += 25.0;
            analysis += "Price at key Fibonacci retracement. ";
        }

        // Order block confluence
        if(IsAtBullishOrderBlock(currentPrice, time[index])) {
            signalStrength += 20.0;
            analysis += "Bullish order block confluence. ";
        }

        // Trend strength bonus
        signalStrength += (trendStrength * 0.25);

        if(signalStrength >= MinSignalStrength) {
            signalType = "BUY";
            BuySignalBuffer[index] = low[index] - (20 * _Point);
        }
    }

    //--- Check for bearish signal
    if(!marketStructureBullish && currentTrend == "BEARISH") {
        // Price near resistance level
        if(resistanceLevel > 0 && MathAbs(currentPrice - resistanceLevel) <= (resistanceLevel * 0.002)) {
            signalStrength += 30.0;
            analysis += "Price at strong resistance level. ";
        }

        // Fibonacci retracement level
        if(IsAtFibonacciLevel(currentPrice, false)) {
            signalStrength += 25.0;
            analysis += "Price at key Fibonacci retracement. ";
        }

        // Order block confluence
        if(IsAtBearishOrderBlock(currentPrice, time[index])) {
            signalStrength += 20.0;
            analysis += "Bearish order block confluence. ";
        }

        // Trend strength bonus
        signalStrength += (trendStrength * 0.25);

        if(signalStrength >= MinSignalStrength) {
            signalType = "SELL";
            SellSignalBuffer[index] = high[index] + (20 * _Point);
        }
    }

    //--- Create trading signal
    if(signalType != "" && signalStrength >= MinSignalStrength) {
        TradingSignal newSignal;
        newSignal.time = time[index];
        newSignal.type = signalType;
        newSignal.price = currentPrice;
        newSignal.strength = signalStrength;
        newSignal.analysis = analysis;

        // Calculate stop loss and take profit
        if(signalType == "BUY") {
            newSignal.stopLoss = supportLevel > 0 ? supportLevel - (30 * _Point) : currentPrice - (50 * _Point);
            newSignal.takeProfit = currentPrice + ((currentPrice - newSignal.stopLoss) * 2.0);
        } else {
            newSignal.stopLoss = resistanceLevel > 0 ? resistanceLevel + (30 * _Point) : currentPrice + (50 * _Point);
            newSignal.takeProfit = currentPrice - ((newSignal.stopLoss - currentPrice) * 2.0);
        }

        int size = ArraySize(currentSignals);
        ArrayResize(currentSignals, size + 1);
        currentSignals[size] = newSignal;

        // Send alert
        if(PlaySoundAlerts) {
            Alert("SK Auto Analyzer: " + signalType + " Signal at " + DoubleToString(currentPrice, _Digits) +
                  " | Strength: " + DoubleToString(signalStrength, 1) + "%");
            PlaySound("alert.wav");
        }

        // Print signal information
        Print("Trading Signal Generated: " + signalType + " at " + DoubleToString(currentPrice, _Digits) +
              " | Strength: " + DoubleToString(signalStrength, 1) + "% | " + analysis);
    }
}

//+------------------------------------------------------------------+
//| Check if price is at Fibonacci level                           |
//+------------------------------------------------------------------+
bool IsAtFibonacciLevel(double price, bool bullish) {
    if(ArraySize(fibLevels) == 0) return false;

    for(int i = 0; i < ArraySize(fibLevels); i++) {
        if(MathAbs(price - fibLevels[i].price) <= (price * 0.001)) {
            // Check if it's a relevant retracement level
            if(bullish && (fibLevels[i].percentage == 38.2 || fibLevels[i].percentage == 50.0 || fibLevels[i].percentage == 61.8)) {
                return true;
            }
            if(!bullish && (fibLevels[i].percentage == 38.2 || fibLevels[i].percentage == 50.0 || fibLevels[i].percentage == 61.8)) {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if price is at bullish order block                       |
//+------------------------------------------------------------------+
bool IsAtBullishOrderBlock(double price, datetime currentTime) {
    for(int i = 0; i < ArraySize(orderBlocks); i++) {
        if(orderBlocks[i].isBullish &&
           currentTime >= orderBlocks[i].startTime &&
           currentTime <= orderBlocks[i].endTime &&
           price >= orderBlocks[i].lowPrice &&
           price <= orderBlocks[i].highPrice) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if price is at bearish order block                       |
//+------------------------------------------------------------------+
bool IsAtBearishOrderBlock(double price, datetime currentTime) {
    for(int i = 0; i < ArraySize(orderBlocks); i++) {
        if(!orderBlocks[i].isBullish &&
           currentTime >= orderBlocks[i].startTime &&
           currentTime <= orderBlocks[i].endTime &&
           price >= orderBlocks[i].lowPrice &&
           price <= orderBlocks[i].highPrice) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Draw automatic Fibonacci levels                                 |
//+------------------------------------------------------------------+
void DrawAutomaticFibonacci() {
    if(ArraySize(swingPoints) < 2) return;

    // Find the most recent significant swing high and low
    double recentHigh = 0;
    double recentLow = 999999;
    datetime highTime = 0;
    datetime lowTime = 0;

    int lookbackCount = MathMin(10, ArraySize(swingPoints));

    for(int i = ArraySize(swingPoints) - lookbackCount; i < ArraySize(swingPoints); i++) {
        if(i < 0) continue;

        if(swingPoints[i].isHigh && swingPoints[i].price > recentHigh) {
            recentHigh = swingPoints[i].price;
            highTime = swingPoints[i].time;
        }

        if(!swingPoints[i].isHigh && swingPoints[i].price < recentLow) {
            recentLow = swingPoints[i].price;
            lowTime = swingPoints[i].time;
        }
    }

    if(recentHigh > 0 && recentLow < 999999 && recentHigh > recentLow) {
        // Clear previous Fibonacci levels
        ArrayResize(fibLevels, 0);

        // Calculate Fibonacci levels
        double range = recentHigh - recentLow;
        double fibLevelValues[] = {0.0, 23.6, 38.2, 50.0, 61.8, 78.6, 100.0, 127.2, 161.8, 200.0};

        for(int i = 0; i < ArraySize(fibLevelValues); i++) {
            FibonacciLevel newLevel;
            newLevel.percentage = fibLevelValues[i];
            newLevel.price = recentLow + (range * fibLevelValues[i] / 100.0);
            newLevel.description = DoubleToString(fibLevelValues[i], 1) + "% (" + DoubleToString(newLevel.price, _Digits) + ")";

            int size = ArraySize(fibLevels);
            ArrayResize(fibLevels, size + 1);
            fibLevels[size] = newLevel;

            // Draw Fibonacci line
            string objName = "Fib_" + IntegerToString(i);
            ObjectDelete(0, objName);
            ObjectCreate(0, objName, OBJ_HLINE, 0, 0, newLevel.price);
            ObjectSetInteger(0, objName, OBJPROP_COLOR, FibonacciColor);
            ObjectSetInteger(0, objName, OBJPROP_STYLE, STYLE_DOT);
            ObjectSetInteger(0, objName, OBJPROP_WIDTH, 1);
            ObjectSetString(0, objName, OBJPROP_TEXT, newLevel.description);
        }

        // Draw Fibonacci extensions if enabled
        if(ShowFibExtensions) {
            double extLevels[] = {127.2, 161.8, 200.0, 261.8, 361.8};
            for(int i = 0; i < ArraySize(extLevels); i++) {
                double extPrice = recentLow + (range * extLevels[i] / 100.0);
                string objName = "FibExt_" + IntegerToString(i);
                ObjectDelete(0, objName);
                ObjectCreate(0, objName, OBJ_HLINE, 0, 0, extPrice);
                ObjectSetInteger(0, objName, OBJPROP_COLOR, FibonacciColor);
                ObjectSetInteger(0, objName, OBJPROP_STYLE, STYLE_DASHDOT);
                ObjectSetInteger(0, objName, OBJPROP_WIDTH, 1);
                ObjectSetString(0, objName, OBJPROP_TEXT, "Ext " + DoubleToString(extLevels[i], 1) + "% (" + DoubleToString(extPrice, _Digits) + ")");
            }
        }

        Print("Automatic Fibonacci drawn from " + DoubleToString(recentLow, _Digits) + " to " + DoubleToString(recentHigh, _Digits));
    }
}

//+------------------------------------------------------------------+
//| Draw liquidity zones                                            |
//+------------------------------------------------------------------+
void DrawLiquidityZones() {
    if(ArraySize(swingPoints) < 2) return;

    // Draw liquidity zones around swing highs and lows
    for(int i = 0; i < ArraySize(swingPoints); i++) {
        string objName = "LiquidityZone_" + IntegerToString(i);
        ObjectDelete(0, objName);

        double zoneHigh, zoneLow;
        if(swingPoints[i].isHigh) {
            zoneHigh = swingPoints[i].price + (5 * _Point);
            zoneLow = swingPoints[i].price - (5 * _Point);
        } else {
            zoneHigh = swingPoints[i].price + (5 * _Point);
            zoneLow = swingPoints[i].price - (5 * _Point);
        }

        datetime endTime = swingPoints[i].time + PeriodSeconds() * 20;

        ObjectCreate(0, objName, OBJ_RECTANGLE, 0, swingPoints[i].time, zoneLow, endTime, zoneHigh);
        ObjectSetInteger(0, objName, OBJPROP_COLOR, LiquidityZoneColor);
        ObjectSetInteger(0, objName, OBJPROP_FILL, true);
        ObjectSetInteger(0, objName, OBJPROP_BACK, true);
        ObjectSetInteger(0, objName, OBJPROP_WIDTH, 1);
        ObjectSetString(0, objName, OBJPROP_TEXT, "Liquidity Zone");
    }
}

//+------------------------------------------------------------------+
//| Draw order blocks                                               |
//+------------------------------------------------------------------+
void DrawOrderBlocks() {
    for(int i = 0; i < ArraySize(orderBlocks); i++) {
        string objName = "OrderBlock_" + IntegerToString(i);
        ObjectDelete(0, objName);

        color blockColor = orderBlocks[i].isBullish ? BullishOrderBlockColor : BearishOrderBlockColor;

        ObjectCreate(0, objName, OBJ_RECTANGLE, 0,
                    orderBlocks[i].startTime, orderBlocks[i].lowPrice,
                    orderBlocks[i].endTime, orderBlocks[i].highPrice);
        ObjectSetInteger(0, objName, OBJPROP_COLOR, blockColor);
        ObjectSetInteger(0, objName, OBJPROP_FILL, true);
        ObjectSetInteger(0, objName, OBJPROP_BACK, true);
        ObjectSetInteger(0, objName, OBJPROP_WIDTH, 2);
        ObjectSetString(0, objName, OBJPROP_TEXT,
                       (orderBlocks[i].isBullish ? "Bullish" : "Bearish") + " Order Block (Strength: " +
                       IntegerToString(orderBlocks[i].strength) + ")");
    }
}

//+------------------------------------------------------------------+
//| Update control panel                                            |
//+------------------------------------------------------------------+
void UpdateControlPanel() {
    // Create background rectangle
    string panelBg = "ControlPanel_BG";
    ObjectDelete(0, panelBg);
    ObjectCreate(0, panelBg, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, panelBg, OBJPROP_XDISTANCE, PanelXPosition);
    ObjectSetInteger(0, panelBg, OBJPROP_YDISTANCE, PanelYPosition);
    ObjectSetInteger(0, panelBg, OBJPROP_XSIZE, 250);
    ObjectSetInteger(0, panelBg, OBJPROP_YSIZE, 200);
    ObjectSetInteger(0, panelBg, OBJPROP_BGCOLOR, PanelBackgroundColor);
    ObjectSetInteger(0, panelBg, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, panelBg, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, panelBg, OBJPROP_COLOR, clrBlack);
    ObjectSetInteger(0, panelBg, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, panelBg, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, panelBg, OBJPROP_BACK, false);
    ObjectSetInteger(0, panelBg, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, panelBg, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, panelBg, OBJPROP_HIDDEN, true);

    // Panel title
    string titleLabel = "ControlPanel_Title";
    ObjectDelete(0, titleLabel);
    ObjectCreate(0, titleLabel, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, titleLabel, OBJPROP_XDISTANCE, PanelXPosition + 10);
    ObjectSetInteger(0, titleLabel, OBJPROP_YDISTANCE, PanelYPosition + 10);
    ObjectSetString(0, titleLabel, OBJPROP_TEXT, "SK Auto Analyzer");
    ObjectSetInteger(0, titleLabel, OBJPROP_COLOR, PanelTextColor);
    ObjectSetInteger(0, titleLabel, OBJPROP_FONTSIZE, 12);
    ObjectSetString(0, titleLabel, OBJPROP_FONT, "Arial Bold");

    // Market trend
    string trendLabel = "ControlPanel_Trend";
    ObjectDelete(0, trendLabel);
    ObjectCreate(0, trendLabel, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, trendLabel, OBJPROP_XDISTANCE, PanelXPosition + 10);
    ObjectSetInteger(0, trendLabel, OBJPROP_YDISTANCE, PanelYPosition + 35);
    ObjectSetString(0, trendLabel, OBJPROP_TEXT, "Trend: " + currentTrend);
    color trendColor = (currentTrend == "BULLISH") ? clrGreen : (currentTrend == "BEARISH") ? clrRed : clrGray;
    ObjectSetInteger(0, trendLabel, OBJPROP_COLOR, trendColor);
    ObjectSetInteger(0, trendLabel, OBJPROP_FONTSIZE, 10);

    // Trend strength
    string strengthLabel = "ControlPanel_Strength";
    ObjectDelete(0, strengthLabel);
    ObjectCreate(0, strengthLabel, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, strengthLabel, OBJPROP_XDISTANCE, PanelXPosition + 10);
    ObjectSetInteger(0, strengthLabel, OBJPROP_YDISTANCE, PanelYPosition + 55);
    ObjectSetString(0, strengthLabel, OBJPROP_TEXT, "Strength: " + DoubleToString(trendStrength, 1) + "%");
    ObjectSetInteger(0, strengthLabel, OBJPROP_COLOR, PanelTextColor);
    ObjectSetInteger(0, strengthLabel, OBJPROP_FONTSIZE, 10);

    // Support level
    string supportLabel = "ControlPanel_Support";
    ObjectDelete(0, supportLabel);
    ObjectCreate(0, supportLabel, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, supportLabel, OBJPROP_XDISTANCE, PanelXPosition + 10);
    ObjectSetInteger(0, supportLabel, OBJPROP_YDISTANCE, PanelYPosition + 75);
    ObjectSetString(0, supportLabel, OBJPROP_TEXT, "Support: " + (supportLevel > 0 ? DoubleToString(supportLevel, _Digits) : "N/A"));
    ObjectSetInteger(0, supportLabel, OBJPROP_COLOR, SupportColor);
    ObjectSetInteger(0, supportLabel, OBJPROP_FONTSIZE, 10);

    // Resistance level
    string resistanceLabel = "ControlPanel_Resistance";
    ObjectDelete(0, resistanceLabel);
    ObjectCreate(0, resistanceLabel, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, resistanceLabel, OBJPROP_XDISTANCE, PanelXPosition + 10);
    ObjectSetInteger(0, resistanceLabel, OBJPROP_YDISTANCE, PanelYPosition + 95);
    ObjectSetString(0, resistanceLabel, OBJPROP_TEXT, "Resistance: " + (resistanceLevel > 0 ? DoubleToString(resistanceLevel, _Digits) : "N/A"));
    ObjectSetInteger(0, resistanceLabel, OBJPROP_COLOR, ResistanceColor);
    ObjectSetInteger(0, resistanceLabel, OBJPROP_FONTSIZE, 10);

    // Swing points count
    string swingLabel = "ControlPanel_Swings";
    ObjectDelete(0, swingLabel);
    ObjectCreate(0, swingLabel, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, swingLabel, OBJPROP_XDISTANCE, PanelXPosition + 10);
    ObjectSetInteger(0, swingLabel, OBJPROP_YDISTANCE, PanelYPosition + 115);
    ObjectSetString(0, swingLabel, OBJPROP_TEXT, "Swing Points: " + IntegerToString(ArraySize(swingPoints)));
    ObjectSetInteger(0, swingLabel, OBJPROP_COLOR, PanelTextColor);
    ObjectSetInteger(0, swingLabel, OBJPROP_FONTSIZE, 10);

    // Order blocks count
    string obLabel = "ControlPanel_OrderBlocks";
    ObjectDelete(0, obLabel);
    ObjectCreate(0, obLabel, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, obLabel, OBJPROP_XDISTANCE, PanelXPosition + 10);
    ObjectSetInteger(0, obLabel, OBJPROP_YDISTANCE, PanelYPosition + 135);
    ObjectSetString(0, obLabel, OBJPROP_TEXT, "Order Blocks: " + IntegerToString(ArraySize(orderBlocks)));
    ObjectSetInteger(0, obLabel, OBJPROP_COLOR, PanelTextColor);
    ObjectSetInteger(0, obLabel, OBJPROP_FONTSIZE, 10);

    // Signals count
    string signalsLabel = "ControlPanel_Signals";
    ObjectDelete(0, signalsLabel);
    ObjectCreate(0, signalsLabel, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, signalsLabel, OBJPROP_XDISTANCE, PanelXPosition + 10);
    ObjectSetInteger(0, signalsLabel, OBJPROP_YDISTANCE, PanelYPosition + 155);
    ObjectSetString(0, signalsLabel, OBJPROP_TEXT, "Signals: " + IntegerToString(ArraySize(currentSignals)));
    ObjectSetInteger(0, signalsLabel, OBJPROP_COLOR, PanelTextColor);
    ObjectSetInteger(0, signalsLabel, OBJPROP_FONTSIZE, 10);

    // Last update time
    string timeLabel = "ControlPanel_Time";
    ObjectDelete(0, timeLabel);
    ObjectCreate(0, timeLabel, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, timeLabel, OBJPROP_XDISTANCE, PanelXPosition + 10);
    ObjectSetInteger(0, timeLabel, OBJPROP_YDISTANCE, PanelYPosition + 175);
    ObjectSetString(0, timeLabel, OBJPROP_TEXT, "Updated: " + TimeToString(TimeCurrent(), TIME_MINUTES));
    ObjectSetInteger(0, timeLabel, OBJPROP_COLOR, clrGray);
    ObjectSetInteger(0, timeLabel, OBJPROP_FONTSIZE, 8);
}

//+------------------------------------------------------------------+
//| Cleanup function                                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    //--- Remove all objects created by indicator
    ObjectsDeleteAll(0, "Fib_");
    ObjectsDeleteAll(0, "FibExt_");
    ObjectsDeleteAll(0, "LiquidityZone_");
    ObjectsDeleteAll(0, "OrderBlock_");
    ObjectsDeleteAll(0, "ControlPanel_");

    Comment("");
    Print("SK Advanced Auto Analyzer deinitialized.");
}
